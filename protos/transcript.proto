syntax = "proto3";

package transcript;

// Main transcript chunk containing all sensor data
message TranscriptChunk {
  ChunkType type = 1;
  int64 timestamp = 2;                    // server-received UNIX ms (global order)
  int64 client_timestamp_start = 3;       // first sample in interval
  int64 client_timestamp_end = 4;         // last sample in interval
  bytes payload = 5;                      // one of the modality messages
}

// Enumeration of all supported data modalities
enum ChunkType {
  UNKNOWN = 0;
  AUDIO = 1;
  VIDEO_FRONT = 2;
  VIDEO_ULTRAWIDE = 3;
  IMU = 4;
  LOCATION = 5;
  TEXT = 6;
}

// Audio data: 16-bit LE PCM, 48 kHz mono, 200ms intervals
message AudioData {
  bytes pcm = 1;                          // raw PCM audio data
}

// Video frame: JPEG encoded image
message VideoFrame {
  bytes jpeg = 1;                         // JPEG encoded video frame
}

// IMU data: accelerometer, gyroscope, and gravity
message IMUData {
  float accel_x = 1;                      // acceleration in m/s²
  float accel_y = 2;
  float accel_z = 3;
  float gyro_x = 4;                       // angular velocity in rad/s
  float gyro_y = 5;
  float gyro_z = 6;
  float gravity_x = 7;                    // gravity vector in m/s²
  float gravity_y = 8;
  float gravity_z = 9;
}

// Location data: GPS coordinates and speed
message LocationData {
  double latitude = 1;                    // latitude in degrees
  double longitude = 2;                   // longitude in degrees
  double altitude = 3;                    // altitude in meters
  float speed = 4;                        // speed in m/s
  float accuracy = 5;                     // location accuracy in meters
  int64 timestamp = 6;                    // GPS timestamp
}

// Text data: UTF-8 string messages (server → client)
message TextData {
  string message = 1;                     // UTF-8 encoded text message
} 