// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		7879885C2DF7F69C00AC638A /* transcript.pb.swift in Sources */ = {isa = PBXBuildFile; fileRef = 7879885A2DF7F69C00AC638A /* transcript.pb.swift */; };
		787988652DF7FE2700AC638A /* SwiftProtobuf in Frameworks */ = {isa = PBXBuildFile; productRef = 787988642DF7FE2700AC638A /* SwiftProtobuf */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		787988292DF7EF2300AC638A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 787988132DF7EF2200AC638A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7879881A2DF7EF2200AC638A;
			remoteInfo = "Ambient AI Peripheral";
		};
		787988332DF7EF2300AC638A /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 787988132DF7EF2200AC638A /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 7879881A2DF7EF2200AC638A;
			remoteInfo = "Ambient AI Peripheral";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		7879881B2DF7EF2200AC638A /* Ambient AI Peripheral.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Ambient AI Peripheral.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		787988282DF7EF2300AC638A /* Ambient AI PeripheralTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Ambient AI PeripheralTests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		787988322DF7EF2300AC638A /* Ambient AI PeripheralUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Ambient AI PeripheralUITests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		787988512DF7F13F00AC638A /* README.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		7879885A2DF7F69C00AC638A /* transcript.pb.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = transcript.pb.swift; sourceTree = "<group>"; };
		7879885E2DF7F76D00AC638A /* generate_protos.sh */ = {isa = PBXFileReference; lastKnownFileType = text.script.sh; path = generate_protos.sh; sourceTree = "<group>"; };
		787988692DFA847000AC638A /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7879881D2DF7EF2200AC638A /* Ambient AI Peripheral */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Ambient AI Peripheral";
			sourceTree = "<group>";
		};
		7879882B2DF7EF2300AC638A /* Ambient AI PeripheralTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Ambient AI PeripheralTests";
			sourceTree = "<group>";
		};
		787988352DF7EF2300AC638A /* Ambient AI PeripheralUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Ambient AI PeripheralUITests";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		787988182DF7EF2200AC638A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				787988652DF7FE2700AC638A /* SwiftProtobuf in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		787988252DF7EF2300AC638A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7879882F2DF7EF2300AC638A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		787988122DF7EF2200AC638A = {
			isa = PBXGroup;
			children = (
				787988692DFA847000AC638A /* Info.plist */,
				7879885E2DF7F76D00AC638A /* generate_protos.sh */,
				7879885B2DF7F69C00AC638A /* Generated */,
				787988512DF7F13F00AC638A /* README.md */,
				7879881D2DF7EF2200AC638A /* Ambient AI Peripheral */,
				7879882B2DF7EF2300AC638A /* Ambient AI PeripheralTests */,
				787988352DF7EF2300AC638A /* Ambient AI PeripheralUITests */,
				787988632DF7FE2700AC638A /* Frameworks */,
				7879881C2DF7EF2200AC638A /* Products */,
			);
			sourceTree = "<group>";
		};
		7879881C2DF7EF2200AC638A /* Products */ = {
			isa = PBXGroup;
			children = (
				7879881B2DF7EF2200AC638A /* Ambient AI Peripheral.app */,
				787988282DF7EF2300AC638A /* Ambient AI PeripheralTests.xctest */,
				787988322DF7EF2300AC638A /* Ambient AI PeripheralUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		7879885B2DF7F69C00AC638A /* Generated */ = {
			isa = PBXGroup;
			children = (
				7879885A2DF7F69C00AC638A /* transcript.pb.swift */,
			);
			path = Generated;
			sourceTree = "<group>";
		};
		787988632DF7FE2700AC638A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7879881A2DF7EF2200AC638A /* Ambient AI Peripheral */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7879883C2DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI Peripheral" */;
			buildPhases = (
				787988172DF7EF2200AC638A /* Sources */,
				787988182DF7EF2200AC638A /* Frameworks */,
				787988192DF7EF2200AC638A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7879881D2DF7EF2200AC638A /* Ambient AI Peripheral */,
			);
			name = "Ambient AI Peripheral";
			packageProductDependencies = (
				787988642DF7FE2700AC638A /* SwiftProtobuf */,
			);
			productName = "Ambient AI Peripheral";
			productReference = 7879881B2DF7EF2200AC638A /* Ambient AI Peripheral.app */;
			productType = "com.apple.product-type.application";
		};
		787988272DF7EF2300AC638A /* Ambient AI PeripheralTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7879883F2DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI PeripheralTests" */;
			buildPhases = (
				787988242DF7EF2300AC638A /* Sources */,
				787988252DF7EF2300AC638A /* Frameworks */,
				787988262DF7EF2300AC638A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				7879882A2DF7EF2300AC638A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				7879882B2DF7EF2300AC638A /* Ambient AI PeripheralTests */,
			);
			name = "Ambient AI PeripheralTests";
			packageProductDependencies = (
			);
			productName = "Ambient AI PeripheralTests";
			productReference = 787988282DF7EF2300AC638A /* Ambient AI PeripheralTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		787988312DF7EF2300AC638A /* Ambient AI PeripheralUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 787988422DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI PeripheralUITests" */;
			buildPhases = (
				7879882E2DF7EF2300AC638A /* Sources */,
				7879882F2DF7EF2300AC638A /* Frameworks */,
				787988302DF7EF2300AC638A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				787988342DF7EF2300AC638A /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				787988352DF7EF2300AC638A /* Ambient AI PeripheralUITests */,
			);
			name = "Ambient AI PeripheralUITests";
			packageProductDependencies = (
			);
			productName = "Ambient AI PeripheralUITests";
			productReference = 787988322DF7EF2300AC638A /* Ambient AI PeripheralUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		787988132DF7EF2200AC638A /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1640;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					7879881A2DF7EF2200AC638A = {
						CreatedOnToolsVersion = 16.4;
					};
					787988272DF7EF2300AC638A = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7879881A2DF7EF2200AC638A;
					};
					787988312DF7EF2300AC638A = {
						CreatedOnToolsVersion = 16.4;
						TestTargetID = 7879881A2DF7EF2200AC638A;
					};
				};
			};
			buildConfigurationList = 787988162DF7EF2200AC638A /* Build configuration list for PBXProject "Ambient AI Peripheral" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 787988122DF7EF2200AC638A;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				787988602DF7FA1A00AC638A /* XCRemoteSwiftPackageReference "swift-protobuf" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7879881C2DF7EF2200AC638A /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7879881A2DF7EF2200AC638A /* Ambient AI Peripheral */,
				787988272DF7EF2300AC638A /* Ambient AI PeripheralTests */,
				787988312DF7EF2300AC638A /* Ambient AI PeripheralUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		787988192DF7EF2200AC638A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		787988262DF7EF2300AC638A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		787988302DF7EF2300AC638A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		787988172DF7EF2200AC638A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7879885C2DF7F69C00AC638A /* transcript.pb.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		787988242DF7EF2300AC638A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		7879882E2DF7EF2300AC638A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		7879882A2DF7EF2300AC638A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7879881A2DF7EF2200AC638A /* Ambient AI Peripheral */;
			targetProxy = 787988292DF7EF2300AC638A /* PBXContainerItemProxy */;
		};
		787988342DF7EF2300AC638A /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 7879881A2DF7EF2200AC638A /* Ambient AI Peripheral */;
			targetProxy = 787988332DF7EF2300AC638A /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		7879883A2DF7EF2300AC638A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7879883B2DF7EF2300AC638A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 65VEZ757KM;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		7879883D2DF7EF2300AC638A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-Peripheral";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7879883E2DF7EF2300AC638A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-Peripheral";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		787988402DF7EF2300AC638A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-PeripheralTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Ambient AI Peripheral.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Ambient AI Peripheral";
			};
			name = Debug;
		};
		787988412DF7EF2300AC638A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.5;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-PeripheralTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Ambient AI Peripheral.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Ambient AI Peripheral";
			};
			name = Release;
		};
		787988432DF7EF2300AC638A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-PeripheralUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Ambient AI Peripheral";
			};
			name = Debug;
		};
		787988442DF7EF2300AC638A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 65VEZ757KM;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "sap.Ambient-AI-PeripheralUITests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = "Ambient AI Peripheral";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		787988162DF7EF2200AC638A /* Build configuration list for PBXProject "Ambient AI Peripheral" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7879883A2DF7EF2300AC638A /* Debug */,
				7879883B2DF7EF2300AC638A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7879883C2DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI Peripheral" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7879883D2DF7EF2300AC638A /* Debug */,
				7879883E2DF7EF2300AC638A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7879883F2DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI PeripheralTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				787988402DF7EF2300AC638A /* Debug */,
				787988412DF7EF2300AC638A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		787988422DF7EF2300AC638A /* Build configuration list for PBXNativeTarget "Ambient AI PeripheralUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				787988432DF7EF2300AC638A /* Debug */,
				787988442DF7EF2300AC638A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		787988602DF7FA1A00AC638A /* XCRemoteSwiftPackageReference "swift-protobuf" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/apple/swift-protobuf.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.30.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		787988642DF7FE2700AC638A /* SwiftProtobuf */ = {
			isa = XCSwiftPackageProductDependency;
			package = 787988602DF7FA1A00AC638A /* XCRemoteSwiftPackageReference "swift-protobuf" */;
			productName = SwiftProtobuf;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 787988132DF7EF2200AC638A /* Project object */;
}
