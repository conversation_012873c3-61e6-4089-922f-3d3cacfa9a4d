//
//  IMUDataSource.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import Foundation
import CoreMotion
import Combine

class IMUDataSource: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isActive: Bool = false
    @Published var currentAcceleration: (x: Double, y: Double, z: Double) = (0, 0, 0)
    @Published var currentGyroscope: (x: Double, y: Double, z: Double) = (0, 0, 0)
    @Published var currentGravity: (x: Double, y: Double, z: Double) = (0, 0, 0)
    @Published var lastUpdateTime: Date = Date()
    
    // MARK: - Private Properties
    private let motionManager = CMMotionManager()
    private let updateInterval: TimeInterval = 0.02 // 50 Hz
    private var dataBuffer: [IMUReading] = []
    private let bufferSize = 10 // Keep last 10 readings
    
    // MARK: - Data Models
    struct IMUReading {
        let timestamp: Date
        let acceleration: (x: Double, y: Double, z: Double)
        let gyroscope: (x: Double, y: Double, z: Double)
        let gravity: (x: Double, y: Double, z: Double)
    }
    
    // MARK: - Initialization
    init() {
        setupMotionManager()
    }
    
    deinit {
        stopCollecting()
    }
    
    // MARK: - Public Methods
    
    /// Start collecting IMU data
    func startCollecting() {
        guard motionManager.isDeviceMotionAvailable else {
            print("❌ IMUDataSource: Device motion not available")
            return
        }
        
        guard !motionManager.isDeviceMotionActive else {
            print("⚠️ IMUDataSource: Already collecting data")
            return
        }
        
        print("▶️ IMUDataSource: Starting IMU data collection")
        
        motionManager.startDeviceMotionUpdates(using: .xArbitraryZVertical, to: .main) { [weak self] (motion, error) in
            guard let self = self, let motion = motion else {
                if let error = error {
                    print("❌ IMUDataSource: Motion update error: \(error)")
                }
                return
            }
            
            self.processMotionData(motion)
        }
        
        isActive = true
    }
    
    /// Stop collecting IMU data
    func stopCollecting() {
        guard motionManager.isDeviceMotionActive else {
            return
        }
        
        print("⏹️ IMUDataSource: Stopping IMU data collection")
        
        motionManager.stopDeviceMotionUpdates()
        isActive = false
        dataBuffer.removeAll()
    }
    
    /// Get current IMU data as protobuf structure
    func getCurrentIMUData() -> Transcript_IMUData {
        var imuData = Transcript_IMUData()
        
        imuData.accelX = Float(currentAcceleration.x)
        imuData.accelY = Float(currentAcceleration.y)
        imuData.accelZ = Float(currentAcceleration.z)
        
        imuData.gyroX = Float(currentGyroscope.x)
        imuData.gyroY = Float(currentGyroscope.y)
        imuData.gyroZ = Float(currentGyroscope.z)
        
        imuData.gravityX = Float(currentGravity.x)
        imuData.gravityY = Float(currentGravity.y)
        imuData.gravityZ = Float(currentGravity.z)
        
        return imuData
    }
    
    /// Get buffered IMU readings
    func getBufferedReadings() -> [IMUReading] {
        return Array(dataBuffer)
    }
    
    /// Clear the data buffer
    func clearBuffer() {
        dataBuffer.removeAll()
    }
    
    // MARK: - Private Methods
    
    private func setupMotionManager() {
        motionManager.deviceMotionUpdateInterval = updateInterval
        print("🔧 IMUDataSource: Motion manager configured (update interval: \(updateInterval)s)")
    }
    
    private func processMotionData(_ motion: CMDeviceMotion) {
        let timestamp = Date()
        
        // Extract acceleration (user acceleration + gravity)
        let totalAccel = (
            x: motion.userAcceleration.x + motion.gravity.x,
            y: motion.userAcceleration.y + motion.gravity.y,
            z: motion.userAcceleration.z + motion.gravity.z
        )
        
        // Extract gyroscope data (rotation rate)
        let gyro = (
            x: motion.rotationRate.x,
            y: motion.rotationRate.y,
            z: motion.rotationRate.z
        )
        
        // Extract gravity vector
        let gravity = (
            x: motion.gravity.x,
            y: motion.gravity.y,
            z: motion.gravity.z
        )
        
        // Update published properties on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentAcceleration = totalAccel
            self?.currentGyroscope = gyro
            self?.currentGravity = gravity
            self?.lastUpdateTime = timestamp
        }
        
        // Add to buffer
        let reading = IMUReading(
            timestamp: timestamp,
            acceleration: totalAccel,
            gyroscope: gyro,
            gravity: gravity
        )
        
        addToBuffer(reading)
        
        // Debug logging (can be removed later)
        if dataBuffer.count % 50 == 0 { // Log every 50 readings (~5 seconds at 10Hz)
            print("📊 IMUDataSource: Collected \(dataBuffer.count) readings")
        }
    }
    
    private func addToBuffer(_ reading: IMUReading) {
        dataBuffer.append(reading)
        
        // Keep buffer size limited
        if dataBuffer.count > bufferSize {
            dataBuffer.removeFirst()
        }
    }
}

// MARK: - Motion Manager Availability Check
extension IMUDataSource {
    /// Check if IMU capabilities are available
    var isMotionAvailable: Bool {
        return motionManager.isDeviceMotionAvailable
    }
    
    /// Check if accelerometer is available
    var isAccelerometerAvailable: Bool {
        return motionManager.isAccelerometerAvailable
    }
    
    /// Check if gyroscope is available
    var isGyroscopeAvailable: Bool {
        return motionManager.isGyroAvailable
    }
    
    /// Get current motion authorization status
    var motionAuthorizationStatus: String {
        if #available(iOS 11.0, *) {
            switch CMMotionActivityManager.authorizationStatus() {
            case .notDetermined:
                return "Not Determined"
            case .restricted:
                return "Restricted"
            case .denied:
                return "Denied"
            case .authorized:
                return "Authorized"
            @unknown default:
                return "Unknown"
            }
        } else {
            return "iOS 11+ Required"
        }
    }
}
