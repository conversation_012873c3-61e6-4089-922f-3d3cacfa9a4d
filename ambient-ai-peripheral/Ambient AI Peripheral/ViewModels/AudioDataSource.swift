//
//  AudioDataSource.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/11/25.
//

import Foundation
import AVFoundation
import Combine

class AudioDataSource: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isActive: Bool = false
    @Published var currentDecibels: Float = -160.0 // -160 dB is silence
    @Published var lastUpdateTime: Date = Date()
    @Published var averageDecibels: Float = -160.0
    @Published var peakDecibels: Float = -160.0
    
    // MARK: - Private Properties
    private var audioEngine: AVAudioEngine?
    private var inputNode: AVAudioInputNode?
    private var inputFormat: AVAudioFormat?
    private var processingFormat: AVAudioFormat?
    private let targetSampleRate: Double = 44100 // 44.1 kHz for output
    private let channelCount: UInt32 = 1 // Mono
    
    // Audio buffer management
    private var audioBuffer: [Int16] = []
    private var chunkSize: Int = 44100 // 1 second at 44.1kHz target rate
    private let bufferLock = NSLock()
    
    // Format conversion
    private var actualSampleRate: Double = 44100
    private var sampleRateRatio: Double = 1.0
    
    // Callback for chunk ready notification
    var onChunkReady: ((Transcript_AudioData) -> Void)?
    
    // Decibel calculation
    private var decibelHistory: [Float] = []
    private let decibelHistorySize = 10
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupAudioEngine()
        AppLogger.shared.debug("🎤 AudioDataSource: Initialized", category: "Audio")
    }
    
    deinit {
        stopCollecting()
    }
    
    // MARK: - Public Methods
    
    /// Start collecting audio data
    func startCollecting() {
        AppLogger.shared.info("▶️ AudioDataSource: Starting audio data collection", category: "Audio")
        
        guard let audioEngine = audioEngine else {
            AppLogger.shared.error("❌ AudioDataSource: Audio engine not available", category: "Audio")
            return
        }
        
        guard !audioEngine.isRunning else {
            AppLogger.shared.warning("⚠️ AudioDataSource: Audio engine already running", category: "Audio")
            return
        }
        
        // Request microphone permission
        AVAudioSession.sharedInstance().requestRecordPermission { [weak self] granted in
            DispatchQueue.main.async {
                if granted {
                    self?.startAudioEngine()
                } else {
                    AppLogger.shared.error("❌ AudioDataSource: Microphone permission denied", category: "Audio")
                }
            }
        }
    }
    
    /// Stop collecting audio data
    func stopCollecting() {
        AppLogger.shared.info("⏹️ AudioDataSource: Stopping audio data collection", category: "Audio")
        
        audioEngine?.stop()
        inputNode?.removeTap(onBus: 0)
        
        bufferLock.lock()
        audioBuffer.removeAll()
        bufferLock.unlock()
        
        DispatchQueue.main.async { [weak self] in
            self?.isActive = false
            self?.currentDecibels = -160.0
            self?.averageDecibels = -160.0
            self?.peakDecibels = -160.0
        }
    }
    
    /// Get current audio data as protobuf structure (1 second chunk)
    private func createAudioChunk() -> Transcript_AudioData? {
        bufferLock.lock()
        defer { bufferLock.unlock() }
        
        guard audioBuffer.count >= chunkSize else {
            return nil
        }
        
        // Extract a 1-second chunk
        let chunk = Array(audioBuffer.prefix(chunkSize))
        audioBuffer.removeFirst(chunkSize)
        
        // Convert Int16 array to Data (16-bit PCM)
        let pcmData = Data(bytes: chunk, count: chunk.count * MemoryLayout<Int16>.size)
        var audioData = Transcript_AudioData()
        audioData.pcm = pcmData
        return audioData
    }
    
    /// Check if a chunk is ready to be sent
    var hasChunkReady: Bool {
        bufferLock.lock()
        defer { bufferLock.unlock() }
        return audioBuffer.count >= chunkSize
    }
    
    /// Clear the audio buffer
    func clearBuffer() {
        bufferLock.lock()
        audioBuffer.removeAll()
        bufferLock.unlock()
        AppLogger.shared.debug("🗑️ AudioDataSource: Buffer cleared", category: "Audio")
    }
    
    /// Get current buffer size
    var currentBufferSize: Int {
        bufferLock.lock()
        defer { bufferLock.unlock() }
        return audioBuffer.count
    }
    
    /// Get actual hardware sample rate
    var hardwareSampleRate: Double {
        return actualSampleRate
    }
    
    // MARK: - Private Methods
    
    private func setupAudioEngine() {
        do {
            // Configure audio session
            let audioSession = AVAudioSession.sharedInstance()
            try audioSession.setCategory(.record, mode: .measurement, options: [])
            try audioSession.setActive(true)
            
            // Create audio engine and get input node
            audioEngine = AVAudioEngine()
            guard let audioEngine = audioEngine else { return }
            
            inputNode = audioEngine.inputNode
            guard let inputNode = inputNode else {
                AppLogger.shared.error("❌ AudioDataSource: Input node not available", category: "Audio")
                return
            }
            
            // Get the hardware input format
            inputFormat = inputNode.inputFormat(forBus: 0)
            guard let inputFormat = inputFormat else {
                AppLogger.shared.error("❌ AudioDataSource: Failed to get input format", category: "Audio")
                return
            }
            
            actualSampleRate = inputFormat.sampleRate
            sampleRateRatio = targetSampleRate / actualSampleRate
            
            // Keep chunk size as 1 second at target sample rate (44.1kHz)
            // This ensures consistent 1-second chunks regardless of hardware sample rate
            
            // Create processing format that matches input but in Float32 for processing
            processingFormat = AVAudioFormat(
                commonFormat: .pcmFormatFloat32,
                sampleRate: actualSampleRate,
                channels: min(inputFormat.channelCount, channelCount),
                interleaved: false
            )
            
            guard let processingFormat = processingFormat else {
                AppLogger.shared.error("❌ AudioDataSource: Failed to create processing format", category: "Audio")
                return
            }
            
            AppLogger.shared.debug("🔧 AudioDataSource: Audio engine configured", category: "Audio")
            AppLogger.shared.debug("🔧 Hardware format: \(inputFormat.sampleRate)Hz, \(inputFormat.channelCount)ch, \(inputFormat.commonFormat.rawValue)", category: "Audio")
            AppLogger.shared.debug("🔧 Processing format: \(processingFormat.sampleRate)Hz, \(processingFormat.channelCount)ch", category: "Audio")
            
        } catch {
            AppLogger.shared.error("❌ AudioDataSource: Failed to setup audio engine: \(error)", category: "Audio")
        }
    }
    
    private func startAudioEngine() {
        guard let audioEngine = audioEngine,
              let inputNode = inputNode,
              let processingFormat = processingFormat else {
            AppLogger.shared.error("❌ AudioDataSource: Audio engine components not available", category: "Audio")
            return
        }
        
        // Calculate buffer size for ~100ms of audio
        let bufferSize = AVAudioFrameCount(actualSampleRate * 0.1)
        
        // Install tap on input node using hardware compatible format
        inputNode.installTap(onBus: 0, bufferSize: bufferSize, format: processingFormat) { [weak self] (buffer, time) in
            self?.processAudioBuffer(buffer)
        }
        
        do {
            // Prepare and start the audio engine
            audioEngine.prepare()
            try audioEngine.start()
            
            DispatchQueue.main.async { [weak self] in
                self?.isActive = true
            }
            
            AppLogger.shared.info("✅ AudioDataSource: Audio engine started successfully", category: "Audio")
            
        } catch {
            AppLogger.shared.error("❌ AudioDataSource: Failed to start audio engine: \(error)", category: "Audio")
        }
    }
    
    private func processAudioBuffer(_ buffer: AVAudioPCMBuffer) {
        guard let channelData = buffer.floatChannelData?[0] else { return }
        
        let frameCount = Int(buffer.frameLength)
        let floatSamples = Array(UnsafeBufferPointer(start: channelData, count: frameCount))
        
        // Convert Float32 samples to Int16 and potentially resample
        let int16Samples = convertToInt16Samples(floatSamples)
        
        // Add samples to buffer
        bufferLock.lock()
        audioBuffer.append(contentsOf: int16Samples)
        
        // Check if we have enough data for a chunk and trigger callback
        if audioBuffer.count >= chunkSize {
            bufferLock.unlock()
            
            // Create and send chunk via callback
            if let audioChunk = createAudioChunk() {
                AppLogger.shared.debug("🎤 AudioDataSource: 1-second chunk ready, triggering callback", category: "Audio")
                onChunkReady?(audioChunk)
            }
        } else {
            bufferLock.unlock()
        }
        
        // Calculate decibels from current samples
        let decibels = calculateDecibels(from: int16Samples)
        
        // Update decibel history for averaging
        decibelHistory.append(decibels)
        if decibelHistory.count > decibelHistorySize {
            decibelHistory.removeFirst()
        }
        
        let avgDb = decibelHistory.reduce(0, +) / Float(decibelHistory.count)
        let peakDb = decibelHistory.max() ?? -160.0
        
        // Update published properties on main thread
        DispatchQueue.main.async { [weak self] in
            self?.currentDecibels = decibels
            self?.averageDecibels = avgDb
            self?.peakDecibels = peakDb
            self?.lastUpdateTime = Date()
        }
        
        // Debug logging for buffer management
        let bufferSizeSeconds = Double(currentBufferSize) / targetSampleRate
        if Int(bufferSizeSeconds * 10) % 20 == 0 { // Log every ~2 seconds
            AppLogger.shared.debug("📊 AudioDataSource: Buffer: dB: \(String(format: "%.1f", decibels)), HW: \(String(format: "%.0f", actualSampleRate))Hz", category: "Audio")
        }
    }
    
    private func convertToInt16Samples(_ floatSamples: [Float]) -> [Int16] {
        // If sample rates match, direct conversion
        if abs(sampleRateRatio - 1.0) < 0.001 {
            return floatSamples.map { sample in
                let clampedSample = max(-1.0, min(1.0, sample))
                return Int16(clampedSample * Float(Int16.max))
            }
        }
        
        // Simple sample rate conversion (basic linear interpolation)
        let outputSampleCount = Int(Double(floatSamples.count) * sampleRateRatio)
        var resampledSamples: [Int16] = []
        resampledSamples.reserveCapacity(outputSampleCount)
        
        for i in 0..<outputSampleCount {
            let sourceIndex = Double(i) / sampleRateRatio
            let lowerIndex = Int(floor(sourceIndex))
            let upperIndex = min(lowerIndex + 1, floatSamples.count - 1)
            let fraction = Float(sourceIndex - Double(lowerIndex))
            
            let lowerSample = floatSamples[lowerIndex]
            let upperSample = floatSamples[upperIndex]
            let interpolatedSample = lowerSample + (upperSample - lowerSample) * fraction
            
            let clampedSample = max(-1.0, min(1.0, interpolatedSample))
            resampledSamples.append(Int16(clampedSample * Float(Int16.max)))
        }
        
        return resampledSamples
    }
    
    private func calculateDecibels(from samples: [Int16]) -> Float {
        guard !samples.isEmpty else { return -160.0 }
        
        // Calculate RMS (Root Mean Square)
        let sumOfSquares = samples.reduce(0.0) { sum, sample in
            let normalized = Float(sample) / Float(Int16.max)
            return sum + Double(normalized * normalized)
        }
        
        let rms = sqrt(Float(sumOfSquares) / Float(samples.count))
        
        // Convert to decibels (with minimum threshold to avoid log(0))
        let minRMS: Float = 1e-8 // Very small value to prevent -infinity
        let clampedRMS = max(rms, minRMS)
        let decibels = 20.0 * log10(clampedRMS)
        
        // Clamp decibels to reasonable range
        return max(decibels, -160.0)
    }
}

// MARK: - Audio Permission Check
extension AudioDataSource {
    /// Check current microphone permission status
    var microphonePermissionStatus: String {
        switch AVAudioSession.sharedInstance().recordPermission {
        case .granted:
            return "Granted"
        case .denied:
            return "Denied"
        case .undetermined:
            return "Not Requested"
        @unknown default:
            return "Unknown"
        }
    }
    
    /// Check if microphone is available
    var isMicrophoneAvailable: Bool {
        return AVAudioSession.sharedInstance().isInputAvailable
    }
}
