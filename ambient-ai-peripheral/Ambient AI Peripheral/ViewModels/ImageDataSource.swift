//
//  ImageDataSource.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/12/25.
//

import Foundation
import AVFoundation
import UIKit
import Combine

class ImageDataSource: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    @Published var isActive: Bool = false
    @Published var frontCameraActive: Bool = false
    @Published var backCameraActive: Bool = false
    @Published var lastCaptureTime: Date = Date()
    @Published var frontPreviewImage: UIImage?
    @Published var backPreviewImage: UIImage?
    
    // MARK: - Private Properties
    private var multiCamSession: AVCaptureMultiCamSession?
    private var frontVideoOutput: AVCaptureVideoDataOutput?
    private var backVideoOutput: AVCaptureVideoDataOutput?
    private var frontCamera: AVCaptureDevice?
    private var backUltrawideCamera: AVCaptureDevice?
    
    // Video sampling timing
    private let targetFrameRate: Double = 1.0 // 1 FPS
    private var lastFrontFrameTime: Date = Date.distantPast
    private var lastBackFrameTime: Date = Date.distantPast
    private let jpegQuality: CGFloat = 0.7 // 70% quality for reasonable file size
    
    // Video processing queue
    private let videoProcessingQueue = DispatchQueue(label: "ImageDataSource.videoProcessing", qos: .userInitiated)
    
    // Callback for image chunk ready notification
    var onImageChunkReady: ((Data, AVCaptureDevice.Position) -> Void)?
    
    // MARK: - Initialization
    override init() {
        super.init()
        setupMultiCamSession()
        AppLogger.shared.debug("📷 ImageDataSource: Initialized with AVMultiCamCaptureSession", category: "Camera")
    }
    
    deinit {
        stopCollecting()
    }
    
    // MARK: - Public Methods
    
    /// Start collecting image data from both cameras
    func startCollecting() {
        AppLogger.shared.info("▶️ ImageDataSource: Starting camera data collection", category: "Camera")
        
        // Request camera permission
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                if granted {
                    self?.startMultiCamSession()
                } else {
                    AppLogger.shared.error("❌ ImageDataSource: Camera permission denied", category: "Camera")
                }
            }
        }
    }
    
    /// Stop collecting image data
    func stopCollecting() {
        AppLogger.shared.info("⏹️ ImageDataSource: Stopping camera data collection", category: "Camera")
        
        multiCamSession?.stopRunning()
        
        DispatchQueue.main.async { [weak self] in
            self?.isActive = false
            self?.frontCameraActive = false
            self?.backCameraActive = false
            self?.frontPreviewImage = nil
            self?.backPreviewImage = nil
        }
    }
    
    // MARK: - Private Methods
    
    private func setupMultiCamSession() {
        // Check if multi-cam is supported
        guard AVCaptureMultiCamSession.isMultiCamSupported else {
            AppLogger.shared.error("❌ ImageDataSource: Multi-cam not supported on this device", category: "Camera")
            return
        }
        
        multiCamSession = AVCaptureMultiCamSession()
        guard let multiCamSession = multiCamSession else { return }
        
        multiCamSession.beginConfiguration()
        
        // Setup cameras and outputs
        setupFrontCamera()
        setupBackUltrawideCamera()
        
        multiCamSession.commitConfiguration()
        AppLogger.shared.debug("🔧 ImageDataSource: Multi-cam session configured", category: "Camera")
    }
    
    private func setupFrontCamera() {
        guard let frontCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .front) else {
            AppLogger.shared.error("❌ ImageDataSource: Front camera not available", category: "Camera")
            return
        }
        
        guard let multiCamSession = multiCamSession else { return }
        
        self.frontCamera = frontCamera
        
        do {
            // Add camera input
            let frontInput = try AVCaptureDeviceInput(device: frontCamera)
            if multiCamSession.canAddInput(frontInput) {
                multiCamSession.addInput(frontInput)
            }
            
            // Add video output
            frontVideoOutput = AVCaptureVideoDataOutput()
            if let frontVideoOutput = frontVideoOutput,
               multiCamSession.canAddOutput(frontVideoOutput) {
                multiCamSession.addOutput(frontVideoOutput)
                
                // Configure output for 1 FPS sampling
                frontVideoOutput.setSampleBufferDelegate(self, queue: videoProcessingQueue)
                frontVideoOutput.alwaysDiscardsLateVideoFrames = true
                
                // Set video settings for JPEG output
                frontVideoOutput.videoSettings = [
                    kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
                ]
            }
            
            AppLogger.shared.debug("🔧 ImageDataSource: Front camera configured", category: "Camera")
            
        } catch {
            AppLogger.shared.error("❌ ImageDataSource: Failed to setup front camera: \(error)", category: "Camera")
        }
    }
    
    private func setupBackUltrawideCamera() {
        // Try to get ultrawide camera first, fallback to wide angle
        var backCamera: AVCaptureDevice?
        
        if let ultrawideCamera = AVCaptureDevice.default(.builtInUltraWideCamera, for: .video, position: .back) {
            backCamera = ultrawideCamera
            AppLogger.shared.debug("📷 ImageDataSource: Using ultrawide back camera", category: "Camera")
        } else if let wideCamera = AVCaptureDevice.default(.builtInWideAngleCamera, for: .video, position: .back) {
            backCamera = wideCamera
            AppLogger.shared.debug("📷 ImageDataSource: Using wide back camera (ultrawide not available)", category: "Camera")
        } else {
            AppLogger.shared.error("❌ ImageDataSource: No back camera available", category: "Camera")
            return
        }
        
        guard let multiCamSession = multiCamSession else { return }
        
        self.backUltrawideCamera = backCamera
        
        do {
            // Add camera input
            let backInput = try AVCaptureDeviceInput(device: backCamera!)
            if multiCamSession.canAddInput(backInput) {
                multiCamSession.addInput(backInput)
            }
            
            // Add video output
            backVideoOutput = AVCaptureVideoDataOutput()
            if let backVideoOutput = backVideoOutput,
               multiCamSession.canAddOutput(backVideoOutput) {
                multiCamSession.addOutput(backVideoOutput)
                
                // Configure output for 1 FPS sampling
                backVideoOutput.setSampleBufferDelegate(self, queue: videoProcessingQueue)
                backVideoOutput.alwaysDiscardsLateVideoFrames = true
                
                // Set video settings for JPEG output
                backVideoOutput.videoSettings = [
                    kCVPixelBufferPixelFormatTypeKey as String: kCVPixelFormatType_32BGRA
                ]
            }
            
            AppLogger.shared.debug("🔧 ImageDataSource: Back camera configured", category: "Camera")
            
        } catch {
            AppLogger.shared.error("❌ ImageDataSource: Failed to setup back camera: \(error)", category: "Camera")
        }
    }
    
    private func startMultiCamSession() {
        videoProcessingQueue.async { [weak self] in
            self?.multiCamSession?.startRunning()
            
            DispatchQueue.main.async {
                let isRunning = self?.multiCamSession?.isRunning ?? false
                self?.frontCameraActive = isRunning
                self?.backCameraActive = isRunning
                self?.isActive = isRunning
                
                AppLogger.shared.debug("📹 ImageDataSource: Multi-cam session status - Active: \(isRunning)", category: "Camera")
                
                if isRunning {
                    AppLogger.shared.info("✅ ImageDataSource: Multi-cam session started", category: "Camera")
                } else {
                    AppLogger.shared.error("❌ ImageDataSource: Multi-cam session failed to start", category: "Camera")
                }
            }
        }
    }
    
    // Video frame processing
    private func shouldCaptureFrame(for position: AVCaptureDevice.Position) -> Bool {
        let now = Date()
        let frameInterval = 1.0 / targetFrameRate // 1 second for 1 FPS
        
        switch position {
        case .front:
            if now.timeIntervalSince(lastFrontFrameTime) >= frameInterval {
                lastFrontFrameTime = now
                return true
            }
        case .back:
            if now.timeIntervalSince(lastBackFrameTime) >= frameInterval {
                lastBackFrameTime = now
                return true
            }
        default:
            break
        }
        
        return false
    }
    
    private func processVideoFrame(_ sampleBuffer: CMSampleBuffer, from position: AVCaptureDevice.Position) {
        // Check if we should capture this frame (1 FPS throttling)
        guard shouldCaptureFrame(for: position) else { return }
        
        // Convert sample buffer to UIImage
        guard let imageBuffer = CMSampleBufferGetImageBuffer(sampleBuffer) else {
            AppLogger.shared.error("❌ ImageDataSource: Failed to get image buffer", category: "Camera")
            return
        }
        
        let ciImage = CIImage(cvPixelBuffer: imageBuffer)
        let context = CIContext()
        
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            AppLogger.shared.error("❌ ImageDataSource: Failed to create CGImage", category: "Camera")
            return
        }
        
        let uiImage = UIImage(cgImage: cgImage)
        
        // Convert to JPEG data
        guard let jpegData = uiImage.jpegData(compressionQuality: jpegQuality) else {
            AppLogger.shared.error("❌ ImageDataSource: Failed to compress image to JPEG", category: "Camera")
            return
        }
        
        // Update preview image on main thread
        DispatchQueue.main.async { [weak self] in
            switch position {
            case .front:
                self?.frontPreviewImage = uiImage
            case .back:
                self?.backPreviewImage = uiImage
            default:
                break
            }
            self?.lastCaptureTime = Date()
        }
        
        AppLogger.shared.debug("📷 ImageDataSource: Frame captured (\(position == .front ? "front" : "back"), \(jpegData.count) bytes JPEG)", category: "Camera")
        
        // Trigger callback with JPEG data
        DispatchQueue.main.async { [weak self] in
            self?.onImageChunkReady?(jpegData, position)
        }
    }
}

// MARK: - AVCaptureVideoDataOutputSampleBufferDelegate
extension ImageDataSource: AVCaptureVideoDataOutputSampleBufferDelegate {
    func captureOutput(_ output: AVCaptureOutput, didOutput sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        // Determine which camera this sample came from
        let position: AVCaptureDevice.Position
        
        if output == frontVideoOutput {
            position = .front
        } else if output == backVideoOutput {
            position = .back
        } else {
            return // Unknown output
        }
        
        // Process the video frame
        processVideoFrame(sampleBuffer, from: position)
    }
    
    func captureOutput(_ output: AVCaptureOutput, didDrop sampleBuffer: CMSampleBuffer, from connection: AVCaptureConnection) {
        let position = (output == frontVideoOutput) ? "front" : "back"
        AppLogger.shared.debug("📉 ImageDataSource: Dropped frame from \(position) camera", category: "Camera")
    }
}

// MARK: - Camera Permission Check
extension ImageDataSource {
    /// Check current camera permission status
    var cameraPermissionStatus: String {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            return "Granted"
        case .denied:
            return "Denied"
        case .restricted:
            return "Restricted"
        case .notDetermined:
            return "Not Requested"
        @unknown default:
            return "Unknown"
        }
    }
    
    /// Check if cameras are available
    var areCamerasAvailable: Bool {
        return AVCaptureDevice.default(for: .video) != nil
    }
}
