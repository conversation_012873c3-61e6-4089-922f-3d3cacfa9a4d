//
//  Utils.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/11/25.
//

import SwiftUI
import OSLog

enum LogLevel: String, CaseIterable, Comparable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    
    var priority: Int {
        switch self {
        case .debug: return 0
        case .info: return 1
        case .warning: return 2
        case .error: return 3
        }
    }
    
    static func < (lhs: LogLevel, rhs: LogLevel) -> Bool {
        return lhs.priority < rhs.priority
    }
    
    var color: Color {
        switch self {
        case .debug: return .gray
        case .info: return .blue
        case .warning: return .orange
        case .error: return .red
        }
    }
    
    var emoji: String {
        switch self {
        case .debug: return "🐛"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        }
    }
}

struct LogEntry: Identifiable {
    let id = UUID()
    let timestamp: Date
    let level: LogLevel
    let message: String
    let category: String
    
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss.SSS"
        return formatter.string(from: timestamp)
    }
}

class AppLogger: ObservableObject {
    static let shared = AppLogger()
    
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "AmbientAI", category: "App")
    private let maxLogEntries = 100
    
    @Published private(set) var logEntries: [LogEntry] = []
    
    private init() {}
    
    private func addLogEntry(level: LogLevel, message: String, category: String = "General") {
        DispatchQueue.main.async {
            let entry = LogEntry(timestamp: Date(), level: level, message: message, category: category)
            self.logEntries.append(entry)
            
            // Keep only the most recent entries
            if self.logEntries.count > self.maxLogEntries {
                self.logEntries.removeFirst(self.logEntries.count - self.maxLogEntries)
            }
        }
    }
    
    func debug(_ message: String, category: String = "General") {
        logger.debug("\(message)")
        addLogEntry(level: .debug, message: message, category: category)
    }
    
    func info(_ message: String, category: String = "General") {
        logger.info("\(message)")
        addLogEntry(level: .info, message: message, category: category)
    }
    
    func warning(_ message: String, category: String = "General") {
        logger.warning("\(message)")
        addLogEntry(level: .warning, message: message, category: category)
    }
    
    func error(_ message: String, category: String = "General") {
        logger.error("\(message)")
        addLogEntry(level: .error, message: message, category: category)
    }
    
    func clearLogs() {
        DispatchQueue.main.async {
            self.logEntries.removeAll()
        }
    }
    
    func filteredLogs(for levels: Set<LogLevel>) -> [LogEntry] {
        return logEntries.filter { levels.contains($0.level) }
    }
}