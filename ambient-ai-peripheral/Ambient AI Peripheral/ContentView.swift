//
//  ContentView.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import Foundation
import AVFoundation

struct ContentView: View {
    @StateObject private var serverConnection = ServerConnection()
    @StateObject private var imuDataSource = IMUDataSource()
    @StateObject private var audioDataSource = AudioDataSource()
    @StateObject private var imageDataSource = ImageDataSource()
    @State private var isStreaming = false
    @State private var streamingTimer: Timer?
    
    private let streamingInterval: TimeInterval = 0.2 // 5 Hz streaming rate
    
    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                ServerConnectionCard(serverConnection: serverConnection)
                
                ImageVisualizationCard(imageDataSource: imageDataSource)
                
                AudioVisualizationCard(audioDataSource: audioDataSource)
                
                IMUVisualizationCard(imuDataSource: imuDataSource)
                
                LogsCard()
                
                Spacer(minLength: 50)
            }
            .padding()
        }
        .onReceive(serverConnection.$isConnected) { isConnected in
            handleConnectionChange(isConnected)
        }
    }
    
    private func handleConnectionChange(_ isConnected: Bool) {
        AppLogger.shared.info("🔗 ContentView: handleConnectionChange called with isConnected: \(isConnected)", category: "Connection")
        if isConnected {
            AppLogger.shared.info("🔗 ContentView: Server connected - starting data collection", category: "Connection")
            setupAudioCallback()
            setupImageCallback()
            imuDataSource.startCollecting()
            audioDataSource.startCollecting()
            imageDataSource.startCollecting()
            isStreaming = true
            startStreamingTimer()
            AppLogger.shared.info("📡 ContentView: Data streaming started (isStreaming now: \(isStreaming))", category: "Streaming")
        } else {
            AppLogger.shared.info("🔗 ContentView: Server disconnected - stopping data collection", category: "Connection")
            audioDataSource.onChunkReady = nil
            imageDataSource.onImageChunkReady = nil
            imuDataSource.stopCollecting()
            audioDataSource.stopCollecting()
            imageDataSource.stopCollecting()
            isStreaming = false
            stopStreamingTimer()
            AppLogger.shared.info("📡 ContentView: Data streaming stopped (isStreaming now: \(isStreaming))", category: "Streaming")
        }
    }
    
    private func startStreamingTimer() {
        stopStreamingTimer() // Stop any existing timer
        AppLogger.shared.debug("⏰ ContentView: Starting streaming timer", category: "Timer")
        streamingTimer = Timer.scheduledTimer(withTimeInterval: streamingInterval, repeats: true) { _ in
            if isStreaming && serverConnection.isConnected {
                sendIMUData()
            }
        }
    }
    
    private func stopStreamingTimer() {
        AppLogger.shared.debug("⏰ ContentView: Stopping streaming timer", category: "Timer")
        streamingTimer?.invalidate()
        streamingTimer = nil
    }
    
    private func sendIMUData() {
        guard let webSocketTask = serverConnection.currentWebSocketTask else {
            AppLogger.shared.warning("⚠️ ContentView: No WebSocket task available for sending IMU data", category: "WebSocket")
            return
        }
        
        // Get current IMU data
        let imuData = imuDataSource.getCurrentIMUData()
        
        // Create timestamp
        let now = Int64(Date().timeIntervalSince1970 * 1000) // milliseconds
        
        // Create TranscriptChunk
        var chunk = Transcript_TranscriptChunk()
        chunk.type = .imu
        chunk.clientTimestampStart = now
        chunk.clientTimestampEnd = now
        chunk.payload = try! imuData.serializedData()
        
        // Send data
        Task {
            do {
                let data = try chunk.serializedData()
                try await webSocketTask.send(.data(data))
                
                // Debug logging (can be removed later)
                if Int(now) % 5000 < 200 { // Log roughly every 5 seconds
                    AppLogger.shared.debug("📤 ContentView: Sent IMU data - Accel: (\(imuData.accelX), \(imuData.accelY), \(imuData.accelZ))", category: "IMU")
                }
            } catch {
                AppLogger.shared.error("❌ ContentView: Failed to send IMU data: \(error)", category: "WebSocket")
            }
        }
    }
    
    private func setupAudioCallback() {
        audioDataSource.onChunkReady = { audioData in
            Task { @MainActor in
                self.sendAudioData(audioData)
            }
        }
        AppLogger.shared.debug("📡 ContentView: Audio callback configured", category: "Audio")
    }
    
    private func setupImageCallback() {
        imageDataSource.onImageChunkReady = { imageData, position in
            Task { @MainActor in
                self.sendImageData(imageData, position: position)
            }
        }
        AppLogger.shared.debug("📡 ContentView: Image callback configured", category: "Camera")
    }
    
    private func sendAudioData(_ audioData: Transcript_AudioData) {
        guard let webSocketTask = serverConnection.currentWebSocketTask else {
            AppLogger.shared.warning("⚠️ ContentView: No WebSocket task available for sending audio data", category: "WebSocket")
            return
        }
        
        // Create timestamp
        let now = Int64(Date().timeIntervalSince1970 * 1000) // milliseconds
        
        // Create TranscriptChunk for audio
        var chunk = Transcript_TranscriptChunk()
        chunk.type = .audio
        chunk.clientTimestampStart = now - 1000 // 1 second ago (chunk duration)
        chunk.clientTimestampEnd = now
        chunk.payload = try! audioData.serializedData()
        
        // Send data
        Task {
            do {
                let data = try chunk.serializedData()
                try await webSocketTask.send(.data(data))
                
                // Log each successful send
                let bufferSeconds = Double(audioDataSource.currentBufferSize) / 44100.0
                AppLogger.shared.debug("🎤 ContentView: Sent 1-second audio chunk (\(audioData.pcm.count) bytes), buffer: \(String(format: "%.1f", bufferSeconds))s, dB: \(String(format: "%.1f", audioDataSource.currentDecibels)), HW: \(String(format: "%.0f", audioDataSource.hardwareSampleRate))Hz", category: "Audio")
            } catch {
                AppLogger.shared.error("❌ ContentView: Failed to send audio data: \(error)", category: "WebSocket")
            }
        }
    }
    
    private func sendImageData(_ imageData: Data, position: AVCaptureDevice.Position) {
        guard let webSocketTask = serverConnection.currentWebSocketTask else {
            AppLogger.shared.warning("⚠️ ContentView: No WebSocket task available for sending image data", category: "WebSocket")
            return
        }
        
        // Create timestamp
        let now = Int64(Date().timeIntervalSince1970 * 1000) // milliseconds
        
        // Create TranscriptChunk for image
        var chunk = Transcript_TranscriptChunk()
        chunk.type = position == .front ? .videoFront : .videoUltrawide
        chunk.clientTimestampStart = now
        chunk.clientTimestampEnd = now
        var videoFrame = Transcript_VideoFrame()
        videoFrame.jpeg = imageData
        chunk.payload = try! videoFrame.serializedData()
        
        // Send data
        Task {
            do {
                let data = try chunk.serializedData()
                try await webSocketTask.send(.data(data))
                
                // Log each successful send
                let cameraName = position == .front ? "front" : "back"
                AppLogger.shared.debug("📷 ContentView: Sent image (\(cameraName) camera, \(imageData.count) bytes JPEG)", category: "Camera")
            } catch {
                AppLogger.shared.error("❌ ContentView: Failed to send image data: \(error)", category: "WebSocket")
            }
        }
    }
}

#Preview {
    ContentView()
}
