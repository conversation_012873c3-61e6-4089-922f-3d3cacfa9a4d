//
//  ImageVisualizationCard.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/12/25.
//

import SwiftUI

struct ImageVisualizationCard: View {
    @ObservedObject var imageDataSource: ImageDataSource
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "camera.fill")
                    .foregroundColor(.green)
                Text("Camera Capture")
                    .font(.headline)
                Spacer()
                statusIndicator
            }
            
            // Camera previews
            HStack(spacing: 12) {
                // Front camera preview
                VStack(alignment: .leading, spacing: 8) {
                    Text("Front Camera")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    cameraPreview(
                        image: imageDataSource.frontPreviewImage,
                        isActive: imageDataSource.frontCameraActive,
                        placeholder: "📱"
                    )
                }
                
                // Back camera preview
                VStack(alignment: .leading, spacing: 8) {
                    Text("Back Camera (Ultrawide)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    cameraPreview(
                        image: imageDataSource.backPreviewImage,
                        isActive: imageDataSource.backCameraActive,
                        placeholder: "🔙"
                    )
                }
            }
            
            // Camera status and info
            cameraStatusSection
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
    private var statusIndicator: some View {
        HStack {
            Circle()
                .fill(imageDataSource.isActive ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
            Text(imageDataSource.isActive ? "Capturing" : "Inactive")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func cameraPreview(image: UIImage?, isActive: Bool, placeholder: String) -> some View {
        ZStack {
            // Background
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray5))
                .frame(width: 120, height: 90)
            
            if let image = image {
                // Actual camera image
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 120, height: 90)
                    .clipped()
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isActive ? Color.green : Color.gray, lineWidth: 2)
                    )
            } else {
                // Placeholder when no image
                VStack {
                    Text(placeholder)
                        .font(.title)
                    Text(isActive ? "Starting..." : "Inactive")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
    
    private var cameraStatusSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Camera Status")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("System")
                        .font(.caption)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Permission: \(imageDataSource.cameraPermissionStatus)")
                            .font(.caption2)
                            .monospaced()
                        Text("Available: \(imageDataSource.areCamerasAvailable ? "Yes" : "No")")
                            .font(.caption2)
                            .monospaced()
                        Text("Last Capture: \(formatTime(imageDataSource.lastCaptureTime))")
                            .font(.caption2)
                            .monospaced()
                    }
                    .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Cameras")
                        .font(.caption)
                        .foregroundColor(.green)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        HStack {
                            Circle()
                                .fill(imageDataSource.frontCameraActive ? Color.green : Color.gray)
                                .frame(width: 6, height: 6)
                            Text("Front")
                                .font(.caption2)
                                .monospaced()
                        }
                        
                        HStack {
                            Circle()
                                .fill(imageDataSource.backCameraActive ? Color.green : Color.gray)
                                .frame(width: 6, height: 6)
                            Text("Back")
                                .font(.caption2)
                                .monospaced()
                        }
                        
                        Text("Interval: 1.0s")
                            .font(.caption2)
                            .monospaced()
                    }
                    .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm:ss"
        return formatter.string(from: date)
    }
}

// MARK: - Preview
#Preview {
    ImageVisualizationCard(imageDataSource: ImageDataSource())
        .padding()
}