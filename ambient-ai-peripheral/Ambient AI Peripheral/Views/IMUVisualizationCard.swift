//
//  IMUVisualizationCard.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI
import Charts

struct IMUVisualizationCard: View {
    @ObservedObject var imuDataSource: IMUDataSource
    
    // Chart data storage
    @State private var accelerationData: [ChartDataPoint] = []
    @State private var gyroscopeData: [ChartDataPoint] = []
    @State private var gravityData: [ChartDataPoint] = []
    
    // Chart configuration
    private let maxDataPoints = 100 // Show last 100 data points (2 seconds at 50Hz)
    private let chartHeight: CGFloat = 120
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "gyroscope")
                    .foregroundColor(.blue)
                Text("IMU Data Visualization")
                    .font(.headline)
                Spacer()
                statusIndicator
            }
            
            // Charts
            VStack(spacing: 12) {
                // Acceleration Chart
                chartSection(
                    title: "Acceleration (m/s²)",
                    data: accelerationData,
                    color: .red
                )
                
                // Gyroscope Chart
                chartSection(
                    title: "Gyroscope (rad/s)",
                    data: gyroscopeData,
                    color: .green
                )
                
                // Gravity Chart
                chartSection(
                    title: "Gravity (m/s²)",
                    data: gravityData,
                    color: .blue
                )
            }
            
            // Current Values Display
            currentValuesSection
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onReceive(imuDataSource.$currentAcceleration) { _ in
            updateChartData()
        }
    }
    
    private var statusIndicator: some View {
        HStack {
            Circle()
                .fill(imuDataSource.isActive ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
            Text(imuDataSource.isActive ? "Active" : "Inactive")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private func chartSection(title: String, data: [ChartDataPoint], color: Color) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Chart(data) { point in
                LineMark(
                    x: .value("Time", point.time),
                    y: .value("X", point.x)
                )
                .foregroundStyle(color)
                .lineStyle(StrokeStyle(lineWidth: 1))
                
                LineMark(
                    x: .value("Time", point.time),
                    y: .value("Y", point.y)
                )
                .foregroundStyle(color.opacity(0.7))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [2, 2]))
                
                LineMark(
                    x: .value("Time", point.time),
                    y: .value("Z", point.z)
                )
                .foregroundStyle(color.opacity(0.4))
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [4, 4]))
            }
            .frame(height: chartHeight)
            .chartXAxis(.hidden) // Hide time axis for cleaner look
            .chartYScale(domain: -10.0...10.0) // Fixed Y-axis range
            .chartYAxis {
                AxisMarks(position: .leading, values: [-10.0, -50.0, 0.0, 5.0, 10.0]) { value in
                    AxisValueLabel {
                        Text("\(value.as(Double.self)!, specifier: "%.1f")")
                    }
                }
            }
        }
    }
    
    private var currentValuesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Current Values")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack(spacing: 20) {
                valueDisplay(
                    title: "Accel",
                    values: (
                        x: imuDataSource.currentAcceleration.x,
                        y: imuDataSource.currentAcceleration.y,
                        z: imuDataSource.currentAcceleration.z
                    ),
                    color: .red
                )
                
                valueDisplay(
                    title: "Gyro",
                    values: (
                        x: imuDataSource.currentGyroscope.x,
                        y: imuDataSource.currentGyroscope.y,
                        z: imuDataSource.currentGyroscope.z
                    ),
                    color: .green
                )
                
                valueDisplay(
                    title: "Gravity",
                    values: (
                        x: imuDataSource.currentGravity.x,
                        y: imuDataSource.currentGravity.y,
                        z: imuDataSource.currentGravity.z
                    ),
                    color: .blue
                )
            }
        }
    }
    
    private func valueDisplay(title: String, values: (x: Double, y: Double, z: Double), color: Color) -> some View {
        VStack(alignment: .leading, spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(color)
                .fontWeight(.medium)
            
            VStack(alignment: .leading, spacing: 1) {
                Text("X: \(values.x, specifier: "%.2f")")
                    .font(.caption2)
                    .monospaced()
                Text("Y: \(values.y, specifier: "%.2f")")
                    .font(.caption2)
                    .monospaced()
                Text("Z: \(values.z, specifier: "%.2f")")
                    .font(.caption2)
                    .monospaced()
            }
            .foregroundColor(.secondary)
        }
    }
    
    private func updateChartData() {
        let now = Date()
        
        // Create new data points
        let accelPoint = ChartDataPoint(
            time: now,
            x: imuDataSource.currentAcceleration.x,
            y: imuDataSource.currentAcceleration.y,
            z: imuDataSource.currentAcceleration.z
        )
        
        let gyroPoint = ChartDataPoint(
            time: now,
            x: imuDataSource.currentGyroscope.x,
            y: imuDataSource.currentGyroscope.y,
            z: imuDataSource.currentGyroscope.z
        )
        
        let gravityPoint = ChartDataPoint(
            time: now,
            x: imuDataSource.currentGravity.x,
            y: imuDataSource.currentGravity.y,
            z: imuDataSource.currentGravity.z
        )
        
        // Add new points and maintain max count
        accelerationData.append(accelPoint)
        gyroscopeData.append(gyroPoint)
        gravityData.append(gravityPoint)
        
        // Keep only recent data points
        if accelerationData.count > maxDataPoints {
            accelerationData.removeFirst()
        }
        if gyroscopeData.count > maxDataPoints {
            gyroscopeData.removeFirst()
        }
        if gravityData.count > maxDataPoints {
            gravityData.removeFirst()
        }
    }
}

// MARK: - Chart Data Model
struct ChartDataPoint: Identifiable {
    let id = UUID()
    let time: Date
    let x: Double
    let y: Double
    let z: Double
}

// MARK: - Preview
#Preview {
    IMUVisualizationCard(imuDataSource: IMUDataSource())
        .padding()
}
