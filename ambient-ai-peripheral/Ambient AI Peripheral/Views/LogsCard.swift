//
//  LogsCard.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/11/25.
//

import SwiftUI

struct LogsCard: View {
    @ObservedObject private var logger = AppLogger.shared
    @State private var selectedLogLevel: LogLevel = .debug
    @State private var isExpanded = false
    
    private var filteredLogs: [LogEntry] {
        logger.filteredLogs(for: getLogLevelsForSelection(selectedLogLevel))
    }
    
    private func getLogLevelsForSelection(_ level: LogLevel) -> Set<LogLevel> {
        switch level {
        case .debug:
            return Set(LogLevel.allCases)
        case .info:
            return Set([.info, .warning, .error])
        case .warning:
            return Set([.warning, .error])
        case .error:
            return Set([.error])
        }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "doc.text")
                    .foregroundColor(.purple)
                Text("Logs")
                    .font(.headline)
                Spacer()
                
                Text("\(filteredLogs.count) entries")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Button(action: {
                    withAnimation {
                        isExpanded.toggle()
                    }
                }) {
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.secondary)
                }
            }
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 12) {
                    // Log level filter
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Filter by Level")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                        
                        Picker("Log Level", selection: $selectedLogLevel) {
                            ForEach(LogLevel.allCases.reversed(), id: \.self) { level in
                                Text(level.rawValue).tag(level)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                    }
                    
                    Divider()
                    
                    // Controls
                    HStack {
                        Button("Clear All") {
                            logger.clearLogs()
                        }
                        .font(.caption)
                        .foregroundColor(.red)
                        
                        Spacer()
                        
                        Button("Show All") {
                            selectedLogLevel = .debug
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                    
                    // Logs ScrollView
                    ScrollView([.horizontal, .vertical]) {
                        LazyVStack(alignment: .leading, spacing: 4) {
                            ForEach(filteredLogs.reversed()) { entry in
                                LogEntryView(entry: entry)
                            }
                        }
                        .padding(.vertical, 4)
                        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
                    }
                    .frame(maxHeight: 300)
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
    
}


struct LogEntryView: View {
    let entry: LogEntry
    
    var body: some View {
        HStack(alignment: .top, spacing: 8) {
            // Timestamp
            Text(entry.formattedTimestamp)
                .font(.system(.caption, design: .monospaced))
                .foregroundColor(.secondary)
                .frame(width: 85, alignment: .leading)
                .fixedSize()
            
            // Level indicator
            Text(entry.level.emoji)
                .font(.caption)
                .frame(width: 20)
                .fixedSize()
            
            // Category
            if entry.category != "General" {
                Text("[\(entry.category)]")
                    .font(.system(.caption2, design: .monospaced))
                    .foregroundColor(.secondary)
                    .frame(minWidth: 80, alignment: .leading)
                    .fixedSize()
            } else {
                Text("")
                    .frame(minWidth: 80, alignment: .leading)
                    .fixedSize()
            }
            
            // Message
            Text(entry.message)
                .font(.system(.caption, design: .monospaced))
                .lineLimit(1)
                .fixedSize(horizontal: true, vertical: false)
        }
        .frame(minWidth: 0, maxWidth: .infinity, alignment: .leading)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(entry.level == .error ? Color.red.opacity(0.05) : 
                   entry.level == .warning ? Color.orange.opacity(0.05) : Color.clear)
        .cornerRadius(4)
    }
}

#Preview {
    VStack {
        LogsCard()
        Spacer()
    }
    .padding()
    .onAppear {
        // Add some sample logs for preview
        AppLogger.shared.debug("This is a debug message")
        AppLogger.shared.info("Application started successfully")
        AppLogger.shared.warning("Network connection is slow")
        AppLogger.shared.error("Failed to connect to server")
        AppLogger.shared.info("User interaction logged", category: "UI")
    }
}