//
//  AudioVisualizationCard.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/11/25.
//

import SwiftUI
import Charts

struct AudioVisualizationCard: View {
    @ObservedObject var audioDataSource: AudioDataSource
    
    // Chart data storage
    @State private var waveformData: [WaveformDataPoint] = []
    @State private var decibelData: [DecibelDataPoint] = []
    
    // Chart configuration
    private let maxDataPoints = 100 // Show last 100 data points (~2 seconds)
    private let chartHeight: CGFloat = 120
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                Image(systemName: "waveform")
                    .foregroundColor(.orange)
                Text("Audio Visualization")
                    .font(.headline)
                Spacer()
                statusIndicator
            }
            
            // Charts
            VStack(spacing: 12) {
                // Decibel Level Chart
                decibelChartSection
                
                // Waveform Visualization
                waveformSection
            }
            
            // Current Values Display
            currentValuesSection
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onReceive(audioDataSource.$currentDecibels) { _ in
            updateChartData()
        }
    }
    
    private var statusIndicator: some View {
        HStack {
            Circle()
                .fill(audioDataSource.isActive ? Color.green : Color.gray)
                .frame(width: 8, height: 8)
            Text(audioDataSource.isActive ? "Recording" : "Inactive")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var decibelChartSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Decibel Levels (dB)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Chart(decibelData) { point in
                LineMark(
                    x: .value("Time", point.time),
                    y: .value("Current", point.current)
                )
                .foregroundStyle(.orange)
                .lineStyle(StrokeStyle(lineWidth: 2))
                
                LineMark(
                    x: .value("Time", point.time),
                    y: .value("Average", point.average)
                )
                .foregroundStyle(.blue)
                .lineStyle(StrokeStyle(lineWidth: 1, dash: [3, 3]))
                
                // Peak indicator
                if point.current > -40.0 { // Only show peaks above -40dB
                    PointMark(
                        x: .value("Time", point.time),
                        y: .value("Peak", point.current)
                    )
                    .foregroundStyle(.red)
                    .symbolSize(20)
                }
            }
            .frame(height: chartHeight)
            .chartXAxis(.hidden)
            .chartYScale(domain: -80.0...0.0) // dB range
            .chartYAxis {
                AxisMarks(position: .leading, values: [-80.0, -60.0, -40.0, -20.0, 0.0]) { value in
                    AxisValueLabel {
                        Text("\(value.as(Double.self)!, specifier: "%.0f")")
                    }
                }
            }
        }
    }
    
    private var waveformSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("Audio Level Meter")
                .font(.caption)
                .foregroundColor(.secondary)
            
            HStack(spacing: 8) {
                // Current level bar
                VStack(alignment: .leading, spacing: 4) {
                    Text("Current")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    decibelMeter(
                        level: audioDataSource.currentDecibels,
                        color: colorForDecibel(audioDataSource.currentDecibels)
                    )
                }
                
                // Average level bar
                VStack(alignment: .leading, spacing: 4) {
                    Text("Average")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    decibelMeter(
                        level: audioDataSource.averageDecibels,
                        color: .blue
                    )
                }
                
                // Peak level bar
                VStack(alignment: .leading, spacing: 4) {
                    Text("Peak")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                    
                    decibelMeter(
                        level: audioDataSource.peakDecibels,
                        color: .red
                    )
                }
                
                Spacer()
            }
        }
    }
    
    private func decibelMeter(level: Float, color: Color) -> some View {
        VStack(spacing: 2) {
            // Vertical meter
            ZStack(alignment: .bottom) {
                // Background
                Rectangle()
                    .fill(Color(.systemGray5))
                    .frame(width: 20, height: 80)
                    .cornerRadius(4)
                
                // Level indicator
                Rectangle()
                    .fill(color)
                    .frame(
                        width: 20,
                        height: max(2, CGFloat(normalizeDecibel(level)) * 80)
                    )
                    .cornerRadius(4)
            }
            
            // Value label
            Text("\(level, specifier: "%.0f")")
                .font(.caption2)
                .monospaced()
                .foregroundColor(.secondary)
        }
    }
    
    private func normalizeDecibel(_ db: Float) -> Float {
        // Normalize -80dB to 0dB into 0.0 to 1.0 range
        let minDB: Float = -80.0
        let maxDB: Float = 0.0
        let clamped = max(minDB, min(maxDB, db))
        return (clamped - minDB) / (maxDB - minDB)
    }
    
    private func colorForDecibel(_ db: Float) -> Color {
        switch db {
        case -80.0..<(-60.0):
            return .green
        case -60.0..<(-40.0):
            return .yellow
        case -40.0..<(-20.0):
            return .orange
        default:
            return .red
        }
    }
    
    private var currentValuesSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Audio Status")
                .font(.subheadline)
                .fontWeight(.medium)
            
            HStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Microphone")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Permission: \(audioDataSource.microphonePermissionStatus)")
                            .font(.caption2)
                            .monospaced()
                        Text("Available: \(audioDataSource.isMicrophoneAvailable ? "Yes" : "No")")
                            .font(.caption2)
                            .monospaced()
                        Text("Buffer: \(String(format: "%.1f", Double(audioDataSource.currentBufferSize) / 44100.0))s")
                            .font(.caption2)
                            .monospaced()
                        Text("HW Rate: \(String(format: "%.0f", audioDataSource.hardwareSampleRate))Hz")
                            .font(.caption2)
                            .monospaced()
                    }
                    .foregroundColor(.secondary)
                }
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("Levels (dB)")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text("Current: \(audioDataSource.currentDecibels, specifier: "%.1f")")
                            .font(.caption2)
                            .monospaced()
                        Text("Average: \(audioDataSource.averageDecibels, specifier: "%.1f")")
                            .font(.caption2)
                            .monospaced()
                        Text("Peak: \(audioDataSource.peakDecibels, specifier: "%.1f")")
                            .font(.caption2)
                            .monospaced()
                    }
                    .foregroundColor(.secondary)
                }
                
                Spacer()
            }
        }
    }
    
    private func updateChartData() {
        let now = Date()
        
        // Create new decibel data point
        let decibelPoint = DecibelDataPoint(
            time: now,
            current: audioDataSource.currentDecibels,
            average: audioDataSource.averageDecibels,
            peak: audioDataSource.peakDecibels
        )
        
        // Add new point and maintain max count
        decibelData.append(decibelPoint)
        
        // Keep only recent data points
        if decibelData.count > maxDataPoints {
            decibelData.removeFirst()
        }
        
        // Create waveform visualization data (simplified bars based on decibel levels)
        let waveformPoint = WaveformDataPoint(
            time: now,
            amplitude: normalizeDecibel(audioDataSource.currentDecibels)
        )
        
        waveformData.append(waveformPoint)
        if waveformData.count > maxDataPoints {
            waveformData.removeFirst()
        }
    }
}

// MARK: - Chart Data Models
struct DecibelDataPoint: Identifiable {
    let id = UUID()
    let time: Date
    let current: Float
    let average: Float
    let peak: Float
}

struct WaveformDataPoint: Identifiable {
    let id = UUID()
    let time: Date
    let amplitude: Float // Normalized 0.0 to 1.0
}

// MARK: - Preview
#Preview {
    AudioVisualizationCard(audioDataSource: AudioDataSource())
        .padding()
}