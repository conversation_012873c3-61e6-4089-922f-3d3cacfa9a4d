//
//  ServerConnectionCard.swift
//  Ambient AI Peripheral
//
//  Created by <PERSON><PERSON><PERSON> on 6/9/25.
//

import SwiftUI

struct ServerConnectionCard: View {
    @ObservedObject var serverConnection: ServerConnection
    @State private var serverURL = "ws://************:8000/ws"
    @State private var isConnecting = false
    @State private var connectionError: String?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "network")
                    .foregroundColor(.blue)
                Text("Server Connection")
                    .font(.headline)
                Spacer()
                connectionStatusIndicator
            }
            
            VStack(alignment: .leading, spacing: 12) {
                Text("WebSocket URL")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                TextField("Enter WebSocket URL", text: $serverURL)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                
                if let error = connectionError {
                    Text(error)
                        .font(.caption)
                        .foregroundColor(.red)
                }
                
                HStack {
                    Button(action: {
                        Task {
                            await connectToServer()
                        }
                    }) {
                        HStack {
                            if isConnecting {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: serverConnection.isConnected ? "checkmark.circle" : "play.circle")
                            }
                            Text(serverConnection.isConnected ? "Connected" : "Connect")
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(serverConnection.isConnected ? Color.green : Color.blue)
                        .cornerRadius(8)
                    }
                    .disabled(isConnecting || (serverConnection.isConnected && !isReconnectNeeded))
                    
                    if serverConnection.isConnected {
                        Button(action: {
                            Task {
                                await disconnectFromServer()
                            }
                        }) {
                            HStack {
                                Image(systemName: "stop.circle")
                                Text("Disconnect")
                            }
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.red)
                            .cornerRadius(8)
                        }
                        .disabled(isConnecting)
                    }
                    
                    Spacer()
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
        .onReceive(serverConnection.$connectionState) { state in
            updateUIForConnectionState(state)
        }
    }
    
    private var connectionStatusIndicator: some View {
        HStack {
            Circle()
                .fill(connectionStatusColor)
                .frame(width: 8, height: 8)
            Text(connectionStatusText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var connectionStatusColor: Color {
        switch serverConnection.connectionState {
        case .connected:
            return .green
        case .connecting, .reconnecting:
            return .orange
        case .disconnected:
            return .gray
        case .error:
            return .red
        }
    }
    
    private var connectionStatusText: String {
        switch serverConnection.connectionState {
        case .connected:
            return "Connected"
        case .connecting:
            return "Connecting..."
        case .reconnecting:
            return "Reconnecting..."
        case .disconnected:
            return "Disconnected"
        case .error(let error):
            return "Error"
        }
    }
    
    private var isReconnectNeeded: Bool {
        if case .error = serverConnection.connectionState {
            return true
        }
        return false
    }
    
    private func connectToServer() async {
        guard let url = URL(string: serverURL) else {
            await MainActor.run {
                connectionError = "Invalid WebSocket URL"
            }
            return
        }
        
        await MainActor.run {
            isConnecting = true
            connectionError = nil
        }
        
        do {
            try await serverConnection.connect(to: url)
        } catch {
            await MainActor.run {
                connectionError = error.localizedDescription
            }
        }
        
        await MainActor.run {
            isConnecting = false
        }
    }
    
    private func disconnectFromServer() async {
        await serverConnection.disconnect()
        await MainActor.run {
            connectionError = nil
        }
    }
    
    private func updateUIForConnectionState(_ state: ConnectionState) {
        Task { @MainActor in
            switch state {
            case .connecting, .reconnecting:
                isConnecting = true
                connectionError = nil
            case .connected:
                isConnecting = false
                connectionError = nil
            case .disconnected:
                isConnecting = false
            case .error(let error):
                isConnecting = false
                connectionError = error.localizedDescription
            }
        }
    }
}

#Preview {
    ServerConnectionCard(serverConnection: ServerConnection())
}
