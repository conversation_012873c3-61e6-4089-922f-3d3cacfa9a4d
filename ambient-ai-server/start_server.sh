#!/bin/bash
# Startup script for the Ambient AI Server
# This script sets the required environment variables and starts the server

echo "🚀 Starting Ambient AI Server..."
echo "📍 Working directory: $(pwd)"

# Check if protobuf files exist
if [ ! -f "src/ambient_ai_server/protos/transcript_pb2.py" ]; then
    echo "📦 Generating protobuf files..."
    python generate_protos.py
fi

# Start the server
echo "🌐 Starting FastAPI server..."
python main.py
