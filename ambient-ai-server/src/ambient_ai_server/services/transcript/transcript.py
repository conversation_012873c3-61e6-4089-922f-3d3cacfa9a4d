"""
Transcript management system for storing and retrieving multimodal sensor data.

This module handles the storage of TranscriptChunk data to Parquet files
and provides functionality for session management and data persistence.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq

from .transcript_handler import TranscriptHandler

logger = logging.getLogger(__name__)


class TranscriptManager:
    """Manages transcript sessions and data storage."""
    
    def __init__(self, recordings_dir: str = "recordings"):
        """
        Initialize the transcript manager.
        
        Args:
            recordings_dir: Directory to store Parquet files
        """
        self.recordings_dir = Path(recordings_dir)
        self.recordings_dir.mkdir(exist_ok=True)
        
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.transcript_handler = TranscriptHandler()
        
        # Define Parquet schema for transcript chunks
        self.schema = pa.schema([
            ('session_id', pa.string()),
            ('chunk_index', pa.int64()),
            ('chunk_type', pa.string()),
            ('server_timestamp', pa.int64()),
            ('client_timestamp_start', pa.int64()),
            ('client_timestamp_end', pa.int64()),
            ('chunk_data', pa.binary()),  # Serialized protobuf data
        ])
    
    def start_session(self) -> str:
        """
        Start a new transcript session.
        
        Returns:
            Session ID string
        """
        session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Ensure unique session ID
        counter = 1
        original_session_id = session_id
        while session_id in self.active_sessions:
            session_id = f"{original_session_id}_{counter}"
            counter += 1
        
        self.active_sessions[session_id] = {
            'start_time': datetime.now(),
            'chunk_count': 0,
            'chunks': [],
            'file_path': self.recordings_dir / f"{session_id}_session.parquet"
        }
        
        logger.info(f"Started new session: {session_id}")
        return session_id
    
    async def add_chunk(self, session_id: str, chunk_data: bytes, server_timestamp: int):
        """
        Add a chunk to the specified session.
        
        Args:
            session_id: Session identifier
            chunk_data: Serialized TranscriptChunk protobuf data
            server_timestamp: Server timestamp when chunk was received
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        
        try:
            # Parse chunk to extract metadata
            parsed_chunk = self.transcript_handler.parse_chunk(chunk_data)
            
            # Create chunk record
            chunk_record = {
                'session_id': session_id,
                'chunk_index': session['chunk_count'],
                'chunk_type': parsed_chunk['type'],
                'server_timestamp': server_timestamp,
                'client_timestamp_start': parsed_chunk['client_timestamp_start'],
                'client_timestamp_end': parsed_chunk['client_timestamp_end'],
                'chunk_data': chunk_data
            }
            
            # Add to session
            session['chunks'].append(chunk_record)
            session['chunk_count'] += 1
            
            # Write to Parquet file incrementally
            await self._write_chunk_to_parquet(session['file_path'], chunk_record)
            
            logger.debug(f"Added chunk {session['chunk_count']} to session {session_id}")
            
        except Exception as e:
            logger.error(f"Error adding chunk to session {session_id}: {e}")
            raise
    
    async def _write_chunk_to_parquet(self, file_path: Path, chunk_record: Dict[str, Any]):
        """
        Write a single chunk record to Parquet file.
        
        Args:
            file_path: Path to the Parquet file
            chunk_record: Chunk data to write
        """
        try:
            # Create DataFrame from chunk record
            df = pd.DataFrame([chunk_record])
            
            # Convert to PyArrow table
            table = pa.Table.from_pandas(df, schema=self.schema)
            
            # Write to Parquet file (append mode)
            if file_path.exists():
                # Read existing data and append
                existing_table = pq.read_table(file_path)
                combined_table = pa.concat_tables([existing_table, table])
                pq.write_table(combined_table, file_path, compression='snappy')
            else:
                # Create new file
                pq.write_table(table, file_path, compression='snappy')
                
        except Exception as e:
            logger.error(f"Error writing to Parquet file {file_path}: {e}")
            raise
    
    async def end_session(self, session_id: str):
        """
        End a transcript session and finalize the Parquet file.
        
        Args:
            session_id: Session identifier
        """
        if session_id not in self.active_sessions:
            raise ValueError(f"Session {session_id} not found")
        
        session = self.active_sessions[session_id]
        
        try:
            # Add session metadata to the file
            session_info = {
                'session_id': session_id,
                'start_time': session['start_time'].isoformat(),
                'end_time': datetime.now().isoformat(),
                'total_chunks': session['chunk_count'],
                'file_path': str(session['file_path'])
            }
            
            logger.info(f"Ended session {session_id}: {session['chunk_count']} chunks saved")
            
            # Remove from active sessions
            del self.active_sessions[session_id]
            
        except Exception as e:
            logger.error(f"Error ending session {session_id}: {e}")
            raise
    
    def list_sessions(self) -> Dict[str, Any]:
        """
        List all sessions (active and recorded).
        
        Returns:
            Dictionary with session information
        """
        # Get active sessions
        active = {
            session_id: {
                'status': 'active',
                'start_time': session['start_time'].isoformat(),
                'chunk_count': session['chunk_count']
            }
            for session_id, session in self.active_sessions.items()
        }
        
        # Get recorded sessions from files
        recorded = {}
        for parquet_file in self.recordings_dir.glob("*_session.parquet"):
            session_id = parquet_file.stem.replace("_session", "")
            try:
                # Read basic info from Parquet file
                table = pq.read_table(parquet_file, columns=['session_id', 'server_timestamp'])
                df = table.to_pandas()
                
                recorded[session_id] = {
                    'status': 'recorded',
                    'file_path': str(parquet_file),
                    'chunk_count': len(df),
                    'start_time': pd.to_datetime(df['server_timestamp'].min(), unit='ms').isoformat(),
                    'end_time': pd.to_datetime(df['server_timestamp'].max(), unit='ms').isoformat()
                }
            except Exception as e:
                logger.warning(f"Error reading session file {parquet_file}: {e}")
        
        return {
            'active_sessions': active,
            'recorded_sessions': recorded,
            'total_active': len(active),
            'total_recorded': len(recorded)
        }
    
    def load_session(self, session_id: str) -> List[Dict[str, Any]]:
        """
        Load a recorded session from Parquet file.
        
        Args:
            session_id: Session identifier
            
        Returns:
            List of chunk records
        """
        file_path = self.recordings_dir / f"{session_id}_session.parquet"
        
        if not file_path.exists():
            raise ValueError(f"Session file not found: {file_path}")
        
        try:
            # Read Parquet file
            table = pq.read_table(file_path)
            df = table.to_pandas()
            
            # Convert to list of dictionaries
            chunks = df.to_dict('records')
            
            logger.info(f"Loaded session {session_id}: {len(chunks)} chunks")
            return chunks
            
        except Exception as e:
            logger.error(f"Error loading session {session_id}: {e}")
            raise
