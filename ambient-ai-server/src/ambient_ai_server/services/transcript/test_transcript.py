#!/usr/bin/env python3
"""
Tests for the transcript service.
"""

import tempfile
import shutil
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from ambient_ai_server.services.transcript.transcript import Transcript<PERSON>anager


def test_start_session():
    """Test starting a new session."""
    temp_dir = tempfile.mkdtemp()
    try:
        transcript_manager = TranscriptManager(recordings_dir=temp_dir)
        session_id = transcript_manager.start_session()
        assert session_id is not None
        assert session_id in transcript_manager.active_sessions

        session = transcript_manager.active_sessions[session_id]
        assert session["chunk_count"] == 0
        assert len(session["chunks"]) == 0
        print("✅ Start session test passed")
    finally:
        shutil.rmtree(temp_dir)


def test_list_sessions():
    """Test listing session information."""
    temp_dir = tempfile.mkdtemp()
    try:
        transcript_manager = TranscriptManager(recordings_dir=temp_dir)
        # Create a test session
        session_id = transcript_manager.start_session()

        sessions_info = transcript_manager.list_sessions()
        assert "active_sessions" in sessions_info
        assert "total_active" in sessions_info
        assert sessions_info["total_active"] == 1
        assert len(sessions_info["active_sessions"]) == 1
        print("✅ List sessions test passed")
    finally:
        shutil.rmtree(temp_dir)


if __name__ == "__main__":
    test_start_session()
    test_list_sessions()
    print("🎉 All transcript tests passed!")
