#!/usr/bin/env python3
"""
Tests for the transcript handler.
"""

import time
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from ambient_ai_server.services.transcript.transcript_handler import Transcript<PERSON><PERSON>ler


def test_transcript_handler_initialization():
    """Test TranscriptHandler initialization."""
    handler = TranscriptHandler()
    assert handler is not None


def test_create_audio_chunk():
    """Test creating an audio chunk."""
    handler = TranscriptHandler()
    
    now = int(time.time() * 1000)
    audio_chunk = handler.create_audio_chunk(
        pcm_data=b"test_audio_data",
        client_start_time=now - 200,
        client_end_time=now
    )
    
    assert len(audio_chunk) > 0
    assert isinstance(audio_chunk, bytes)


def test_parse_audio_chunk():
    """Test parsing an audio chunk."""
    handler = TranscriptHandler()
    
    now = int(time.time() * 1000)
    audio_chunk = handler.create_audio_chunk(
        pcm_data=b"test_audio_data",
        client_start_time=now - 200,
        client_end_time=now
    )
    
    parsed = handler.parse_chunk(audio_chunk)
    assert parsed['type'] == 'AUDIO'
    assert parsed['payload']['pcm_size'] == len(b"test_audio_data")


def test_create_video_chunk():
    """Test creating a video chunk."""
    handler = TranscriptHandler()
    
    now = int(time.time() * 1000)
    video_chunk = handler.create_video_chunk(
        jpeg_data=b"fake_jpeg_data",
        camera_type="front",
        client_start_time=now,
        client_end_time=now + 33
    )
    
    assert len(video_chunk) > 0
    assert isinstance(video_chunk, bytes)


def test_create_text_chunk():
    """Test creating a text chunk."""
    handler = TranscriptHandler()
    
    now = int(time.time() * 1000)
    text_chunk = handler.create_text_chunk(
        message="Test message",
        client_start_time=now,
        client_end_time=now + 100
    )
    
    assert len(text_chunk) > 0
    assert isinstance(text_chunk, bytes)


if __name__ == "__main__":
    test_transcript_handler_initialization()
    test_create_audio_chunk()
    test_parse_audio_chunk()
    test_create_video_chunk()
    test_create_text_chunk()
    print("🎉 All transcript handler tests passed!")
