#!/usr/bin/env python3
"""
Tests for the analytics service.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from ambient_ai_server.services.analytics.analytics import AnalyticsTracker


def test_record_chunk():
    """Test recording chunks for analytics."""
    analytics_tracker = AnalyticsTracker()

    # Record some chunks
    analytics_tracker.record_chunk("AUDIO")
    analytics_tracker.record_chunk("VIDEO_FRONT")
    analytics_tracker.record_chunk("AUDIO")

    stats = analytics_tracker.get_stats()

    assert stats["chunks"]["total_received"] == 3
    assert stats["chunks"]["by_modality"]["AUDIO"] == 2
    assert stats["chunks"]["by_modality"]["VIDEO_FRONT"] == 1
    print("✅ Record chunk test passed")


def test_session_tracking():
    """Test session tracking functionality."""
    analytics_tracker = AnalyticsTracker()

    # Start some sessions
    analytics_tracker.start_session("session1")
    analytics_tracker.start_session("session2")

    stats = analytics_tracker.get_stats()

    assert stats["sessions"]["active_count"] == 2
    assert stats["sessions"]["total_sessions"] == 2

    # End a session
    analytics_tracker.end_session("session1")

    stats = analytics_tracker.get_stats()
    assert stats["sessions"]["active_count"] == 1
    assert stats["sessions"]["total_sessions"] == 2
    print("✅ Session tracking test passed")


def test_processing_time_tracking():
    """Test processing time tracking."""
    analytics_tracker = AnalyticsTracker()

    # Record chunks with processing times
    analytics_tracker.record_chunk("AUDIO", processing_time_ms=10.5)
    analytics_tracker.record_chunk("VIDEO_FRONT", processing_time_ms=15.2)

    stats = analytics_tracker.get_stats()

    assert "performance" in stats
    assert stats["performance"]["avg_processing_time_ms"] > 0
    print("✅ Processing time tracking test passed")


if __name__ == "__main__":
    test_record_chunk()
    test_session_tracking()
    test_processing_time_tracking()
    print("🎉 All analytics tests passed!")
