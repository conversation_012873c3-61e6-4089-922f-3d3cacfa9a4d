#!/usr/bin/env python3
"""
Tests for the server service.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from fastapi.testclient import TestClient
from ambient_ai_server.services.server.server import create_app


def test_health_check():
    """Test the health check endpoint."""
    app = create_app()
    client = TestClient(app)

    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    print("✅ Health check test passed")


def test_analytics_endpoint():
    """Test the analytics endpoint."""
    app = create_app()
    client = TestClient(app)

    response = client.get("/analytics")
    assert response.status_code == 200
    data = response.json()
    assert "chunks" in data
    assert "server_info" in data
    print("✅ Analytics endpoint test passed")


def test_sessions_endpoint():
    """Test the sessions endpoint."""
    app = create_app()
    client = TestClient(app)

    response = client.get("/sessions")
    assert response.status_code == 200
    data = response.json()
    assert "active_sessions" in data
    assert "total_active" in data
    print("✅ Sessions endpoint test passed")


if __name__ == "__main__":
    test_health_check()
    test_analytics_endpoint()
    test_sessions_endpoint()
    print("🎉 All server tests passed!")
