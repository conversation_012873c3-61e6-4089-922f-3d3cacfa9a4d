"""
Abstract interface for Voice Activity Detection (VAD) models.

This module defines the common interface that all VAD implementations must follow,
enabling easy switching between different VAD models (Silero VAD, TEN VAD, etc.).
"""

from abc import ABC, abstractmethod
from typing import List, Tuple, Optional, Dict, Any
import numpy as np


class VADInterface(ABC):
    """Abstract base class for Voice Activity Detection models."""
    
    def __init__(self, sample_rate: int = 16000, hop_size: int = 160, threshold: float = 0.5):
        """
        Initialize the VAD model.
        
        Args:
            sample_rate: Audio sample rate in Hz (typically 16000)
            hop_size: Number of samples per frame (160 = 10ms at 16kHz, 256 = 16ms at 16kHz)
            threshold: Speech detection threshold (0.0 to 1.0)
        """
        self.sample_rate = sample_rate
        self.hop_size = hop_size
        self.threshold = threshold
        self._is_initialized = False
    
    @abstractmethod
    def initialize(self) -> None:
        """Initialize the VAD model. Must be called before processing audio."""
        pass
    
    @abstractmethod
    def process_chunk(self, audio_chunk: np.ndarray) -> float:
        """
        Process a single audio chunk and return speech probability.
        
        Args:
            audio_chunk: Audio samples as numpy array (float32, normalized to [-1, 1])
            
        Returns:
            Speech probability (0.0 to 1.0)
        """
        pass
    
    @abstractmethod
    def process_batch(self, audio_chunks: List[np.ndarray]) -> List[float]:
        """
        Process multiple audio chunks and return speech probabilities.
        
        Args:
            audio_chunks: List of audio chunks as numpy arrays
            
        Returns:
            List of speech probabilities (0.0 to 1.0)
        """
        pass
    
    def detect_speech(self, audio_chunk: np.ndarray) -> bool:
        """
        Detect if speech is present in an audio chunk.
        
        Args:
            audio_chunk: Audio samples as numpy array
            
        Returns:
            True if speech is detected, False otherwise
        """
        probability = self.process_chunk(audio_chunk)
        return probability > self.threshold
    
    def detect_speech_batch(self, audio_chunks: List[np.ndarray]) -> List[bool]:
        """
        Detect speech in multiple audio chunks.
        
        Args:
            audio_chunks: List of audio chunks as numpy arrays
            
        Returns:
            List of boolean speech detection results
        """
        probabilities = self.process_batch(audio_chunks)
        return [prob > self.threshold for prob in probabilities]
    
    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """
        Get information about the VAD model.
        
        Returns:
            Dictionary containing model information (name, version, etc.)
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """Clean up model resources."""
        pass
    
    def __enter__(self):
        """Context manager entry."""
        if not self._is_initialized:
            self.initialize()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()
