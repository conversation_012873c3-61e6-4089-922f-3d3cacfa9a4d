#!/usr/bin/env python3
"""
Tests for the replay client.
"""

import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from ambient_ai_server.clients.replay_client import ReplayClient


def test_replay_client_initialization():
    """Test that ReplayClient initializes correctly."""
    replay_client = ReplayClient()
    assert replay_client is not None
    assert hasattr(replay_client, 'server_url')
    print("✅ ReplayClient initialization test passed")


def test_replay_client_methods():
    """Test that ReplayClient has expected methods."""
    replay_client = ReplayClient()

    # Test that the method exists
    assert hasattr(replay_client, 'replay_session')
    print("✅ ReplayClient methods test passed")


if __name__ == "__main__":
    test_replay_client_initialization()
    test_replay_client_methods()
    print("🎉 All replay client tests passed!")
