"""
Replay client for testing the Ambient AI Server with recorded data.

This module provides functionality to replay saved Parquet transcript files
over WebSocket connections, simulating the original data flow.
"""

import asyncio
import argparse
import logging
import time
from pathlib import Path
from typing import List, Dict, Any

import pandas as pd
import pyarrow.parquet as pq
import websockets

from ..services.transcript import TranscriptHandler

logger = logging.getLogger(__name__)


class ReplayClient:
    """Client for replaying recorded transcript sessions."""
    
    def __init__(self, server_url: str = "ws://localhost:8000/ws"):
        """
        Initialize the replay client.
        
        Args:
            server_url: WebSocket URL of the server
        """
        self.server_url = server_url
        self.transcript_handler = TranscriptHandler()
    
    async def replay_session(
        self, 
        parquet_file: Path, 
        fast_mode: bool = False,
        start_chunk: int = 0,
        end_chunk: int = None
    ):
        """
        Replay a recorded session from a Parquet file.
        
        Args:
            parquet_file: Path to the Parquet file containing the session
            fast_mode: If True, send chunks as fast as possible without timing delays
            start_chunk: Index of the first chunk to replay (0-based)
            end_chunk: Index of the last chunk to replay (exclusive), None for all
        """
        if not parquet_file.exists():
            raise FileNotFoundError(f"Parquet file not found: {parquet_file}")
        
        logger.info(f"Loading session from {parquet_file}")
        
        try:
            # Load the Parquet file
            table = pq.read_table(parquet_file)
            df = table.to_pandas()
            
            # Sort by chunk index to ensure proper order
            df = df.sort_values('chunk_index')
            
            # Apply chunk range filtering
            if end_chunk is not None:
                df = df.iloc[start_chunk:end_chunk]
            else:
                df = df.iloc[start_chunk:]
            
            if df.empty:
                logger.warning("No chunks to replay in the specified range")
                return
            
            logger.info(f"Replaying {len(df)} chunks from session")
            
            # Connect to the server
            async with websockets.connect(self.server_url) as websocket:
                logger.info(f"Connected to server at {self.server_url}")
                
                # Receive welcome message
                welcome_msg = await websocket.recv()
                logger.info(f"Server: {welcome_msg}")
                
                # Replay chunks
                await self._replay_chunks(websocket, df, fast_mode)
                
                logger.info("Replay completed successfully")
                
        except Exception as e:
            logger.error(f"Error during replay: {e}")
            raise
    
    async def _replay_chunks(
        self, 
        websocket, 
        df: pd.DataFrame, 
        fast_mode: bool
    ):
        """
        Send chunks to the server with appropriate timing.
        
        Args:
            websocket: WebSocket connection
            df: DataFrame containing chunk data
            fast_mode: Whether to send chunks without timing delays
        """
        start_time = time.time()
        first_chunk_time = None
        
        for index, row in df.iterrows():
            try:
                # Get chunk data
                chunk_data = row['chunk_data']
                chunk_type = row['chunk_type']
                client_timestamp = row['client_timestamp_start']
                
                # Calculate timing for realistic replay
                if not fast_mode:
                    if first_chunk_time is None:
                        first_chunk_time = client_timestamp
                    
                    # Calculate how long to wait based on original timing
                    elapsed_original = client_timestamp - first_chunk_time
                    elapsed_replay = (time.time() - start_time) * 1000  # Convert to ms
                    
                    wait_time = (elapsed_original - elapsed_replay) / 1000  # Convert to seconds
                    if wait_time > 0:
                        await asyncio.sleep(wait_time)
                
                # Send chunk to server
                await websocket.send(chunk_data)
                
                logger.debug(f"Sent {chunk_type} chunk (index: {row['chunk_index']})")
                
                # Small delay even in fast mode to avoid overwhelming the server
                if fast_mode:
                    await asyncio.sleep(0.001)  # 1ms delay
                
            except Exception as e:
                logger.error(f"Error sending chunk {row['chunk_index']}: {e}")
                continue
    
    def analyze_session(self, parquet_file: Path) -> Dict[str, Any]:
        """
        Analyze a recorded session and return statistics.
        
        Args:
            parquet_file: Path to the Parquet file
            
        Returns:
            Dictionary with session analysis
        """
        if not parquet_file.exists():
            raise FileNotFoundError(f"Parquet file not found: {parquet_file}")
        
        try:
            # Load the Parquet file
            table = pq.read_table(parquet_file)
            df = table.to_pandas()
            
            # Basic statistics
            total_chunks = len(df)
            modality_counts = df['chunk_type'].value_counts().to_dict()
            
            # Time analysis
            start_time = pd.to_datetime(df['server_timestamp'].min(), unit='ms')
            end_time = pd.to_datetime(df['server_timestamp'].max(), unit='ms')
            duration = end_time - start_time
            
            # Calculate data rates
            chunks_per_second = total_chunks / duration.total_seconds() if duration.total_seconds() > 0 else 0
            
            # Analyze chunk sizes
            chunk_sizes = df['chunk_data'].apply(len)
            total_bytes = chunk_sizes.sum()
            avg_chunk_size = chunk_sizes.mean()
            
            return {
                'file_info': {
                    'path': str(parquet_file),
                    'size_bytes': parquet_file.stat().st_size
                },
                'session_info': {
                    'total_chunks': total_chunks,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'duration_seconds': duration.total_seconds(),
                    'chunks_per_second': round(chunks_per_second, 2)
                },
                'modality_breakdown': modality_counts,
                'data_size': {
                    'total_bytes': total_bytes,
                    'avg_chunk_size_bytes': round(avg_chunk_size, 2),
                    'total_mb': round(total_bytes / (1024 * 1024), 2)
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing session: {e}")
            raise


async def main():
    """Main function for the replay client CLI."""
    parser = argparse.ArgumentParser(description="Replay recorded Ambient AI sessions")
    parser.add_argument("parquet_file", help="Path to the Parquet file to replay")
    parser.add_argument("--fast", action="store_true", help="Send chunks as fast as possible")
    parser.add_argument("--server", default="ws://localhost:8000/ws", help="Server WebSocket URL")
    parser.add_argument("--start", type=int, default=0, help="Start chunk index (0-based)")
    parser.add_argument("--end", type=int, help="End chunk index (exclusive)")
    parser.add_argument("--analyze", action="store_true", help="Only analyze the file, don't replay")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(level=log_level, format='%(asctime)s - %(levelname)s - %(message)s')
    
    parquet_file = Path(args.parquet_file)
    client = ReplayClient(args.server)
    
    try:
        if args.analyze:
            # Just analyze the file
            analysis = client.analyze_session(parquet_file)
            print("\n📊 Session Analysis:")
            print(f"  File: {analysis['file_info']['path']}")
            print(f"  Total chunks: {analysis['session_info']['total_chunks']}")
            print(f"  Duration: {analysis['session_info']['duration_seconds']:.1f} seconds")
            print(f"  Rate: {analysis['session_info']['chunks_per_second']:.1f} chunks/sec")
            print(f"  Data size: {analysis['data_size']['total_mb']:.1f} MB")
            print("\n  Modality breakdown:")
            for modality, count in analysis['modality_breakdown'].items():
                print(f"    {modality}: {count} chunks")
        else:
            # Replay the session
            await client.replay_session(
                parquet_file=parquet_file,
                fast_mode=args.fast,
                start_chunk=args.start,
                end_chunk=args.end
            )
    
    except KeyboardInterrupt:
        logger.info("Replay interrupted by user")
    except Exception as e:
        logger.error(f"Replay failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
