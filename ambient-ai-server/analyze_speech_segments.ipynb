# Install required packages (run once)
# !pip install pandas pyarrow torch torchaudio numpy matplotlib seaborn ipywidgets soundfile librosa

import pandas as pd
import numpy as np
import torch
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import soundfile as sf
import librosa
import librosa.display
from IPython.display import Audio, display, HTML
import ipywidgets as widgets
from datetime import datetime, timedelta
import sys
import os

# Add the server source to path for imports
sys.path.insert(0, 'src')
from ambient_ai_server.protos import transcript_pb2

# Set up plotting style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")
%matplotlib inline

# Configuration
SAMPLE_RATE = 44100  # 44.1kHz as specified
CHUNK_DURATION_MS = 200  # 200ms chunks
VAD_SAMPLE_RATE = 16000  # VAD model expects 16kHz
VAD_THRESHOLD = 0.05  # Speech detection threshold
DOWNSAMPLE_FACTOR = SAMPLE_RATE // VAD_SAMPLE_RATE  # ~2.76, we'll use 3 for safety

print(f"Audio Configuration:")
print(f"  Sample Rate: {SAMPLE_RATE} Hz")
print(f"  Chunk Duration: {CHUNK_DURATION_MS} ms")
print(f"  VAD Sample Rate: {VAD_SAMPLE_RATE} Hz")
print(f"  Downsample Factor: {DOWNSAMPLE_FACTOR}")
print(f"  VAD Threshold: {VAD_THRESHOLD}")

# Load Silero VAD model
print("Loading Silero VAD model...")
vad_model, _ = torch.hub.load(
    repo_or_dir='snakers4/silero-vad',
    model='silero_vad',
    force_reload=False,
    onnx=False
)
vad_model.eval()
print("✅ VAD model loaded successfully!")

def load_session_data(parquet_file):
    """Load and parse session data from parquet file."""
    print(f"Loading session data from: {parquet_file}")
    
    # Load the parquet file
    df = pd.read_parquet(parquet_file)
    
    print(f"Session info:")
    print(f"  Total chunks: {len(df)}")
    print(f"  Chunk types: {df['chunk_type'].value_counts().to_dict()}")
    # print(f"  Duration: {(df['timestamp'].max() - df['timestamp'].min()) / 1000:.1f} seconds")
    
    return df

def extract_audio_chunks(df):
    """Extract audio chunks from session data."""
    # Filter for audio chunks
    audio_df = df[df['chunk_type'] == 'AUDIO'].copy().sort_values('chunk_index')
    
    print(f"Found {len(audio_df)} audio chunks")
    
    audio_chunks = []
    timestamps = []
    
    for _, row in audio_df.iterrows():
        # Parse the chunk
        chunk = transcript_pb2.TranscriptChunk()
        chunk.ParseFromString(row['chunk_data'])
        
        # Parse the audio data
        audio_data = transcript_pb2.AudioData()
        audio_data.ParseFromString(chunk.payload)
        
        # Convert PCM bytes to numpy array
        pcm_array = np.frombuffer(audio_data.pcm, dtype=np.int16)
        audio_chunks.append(pcm_array)
        timestamps.append(chunk.client_timestamp_start)
    
    return audio_chunks, timestamps

def run_vad_analysis(audio_chunks):
    """Run VAD analysis on audio chunks."""
    print("Running VAD analysis...")
    
    vad_results = []
    vad_probabilities = []
    
    for i, chunk in enumerate(audio_chunks):
        # Downsample to 16kHz for VAD
        downsampled = chunk[::DOWNSAMPLE_FACTOR]
        
        # VAD expects exactly 512 samples for 16kHz
        expected_samples = 512
        if len(downsampled) > expected_samples:
            vad_audio = downsampled[-expected_samples:]
        elif len(downsampled) < expected_samples:
            padding = expected_samples - len(downsampled)
            vad_audio = np.pad(downsampled, (padding, 0), mode='constant', constant_values=0)
        else:
            vad_audio = downsampled
        
        # Convert to float32 and normalize
        audio_float = vad_audio.astype(np.float32) / 32768.0
        
        # Run VAD
        with torch.no_grad():
            audio_tensor = torch.from_numpy(audio_float).unsqueeze(0)
            speech_prob = vad_model(audio_tensor, VAD_SAMPLE_RATE).item()
        
        has_speech = speech_prob > VAD_THRESHOLD
        vad_results.append(has_speech)
        vad_probabilities.append(speech_prob)
        
        if (i + 1) % 10 == 0:
            print(f"  Processed {i + 1}/{len(audio_chunks)} chunks")
    
    print(f"✅ VAD analysis complete!")
    print(f"  Speech detected in {sum(vad_results)}/{len(vad_results)} chunks")
    
    return vad_results, vad_probabilities

def detect_speech_segments(vad_results, timestamps, min_speech_duration_ms=400, min_silence_duration_ms=800):
    """Detect continuous speech segments from VAD results."""
    print("Detecting speech segments...")
    
    segments = []
    current_segment = None
    
    min_speech_chunks = min_speech_duration_ms // CHUNK_DURATION_MS
    min_silence_chunks = min_silence_duration_ms // CHUNK_DURATION_MS
    
    speech_chunk_count = 0
    silence_chunk_count = 0
    
    for i, (has_speech, timestamp) in enumerate(zip(vad_results, timestamps)):
        if has_speech:
            speech_chunk_count += 1
            silence_chunk_count = 0
            
            # Start new segment if we have enough speech
            if current_segment is None and speech_chunk_count >= min_speech_chunks:
                current_segment = {
                    'start_chunk': i - speech_chunk_count + 1,
                    'start_time': timestamps[i - speech_chunk_count + 1],
                    'chunks': []
                }
            
            # Add to current segment
            if current_segment is not None:
                current_segment['chunks'].append(i)
        else:
            silence_chunk_count += 1
            speech_chunk_count = 0
            
            # End current segment if we have enough silence
            if current_segment is not None and silence_chunk_count >= min_silence_chunks:
                current_segment['end_chunk'] = current_segment['chunks'][-1]
                current_segment['end_time'] = timestamps[current_segment['chunks'][-1]] + CHUNK_DURATION_MS
                current_segment['duration_ms'] = current_segment['end_time'] - current_segment['start_time']
                segments.append(current_segment)
                current_segment = None
    
    # Close any remaining segment
    if current_segment is not None:
        current_segment['end_chunk'] = current_segment['chunks'][-1]
        current_segment['end_time'] = timestamps[current_segment['chunks'][-1]] + CHUNK_DURATION_MS
        current_segment['duration_ms'] = current_segment['end_time'] - current_segment['start_time']
        segments.append(current_segment)
    
    print(f"✅ Found {len(segments)} speech segments")
    for i, seg in enumerate(segments):
        print(f"  Segment {i+1}: {seg['duration_ms']/1000:.1f}s ({len(seg['chunks'])} chunks)")
    
    return segments

# Specify the session file path
session_file = Path("/Users/<USER>/ambient-ai-prototype/recordings/20250612_235253_session.parquet")

# You can change this to analyze a different session
# session_file = Path("path/to/your/session.parquet")

if not session_file.exists():
    print(f"❌ Session file not found: {session_file}")
    print("Please update the session_file path above to point to your session.parquet file")
else:
    # Load session data
    df = load_session_data(session_file)
    
    # Extract audio chunks
    audio_chunks, timestamps = extract_audio_chunks(df)
    
    if len(audio_chunks) == 0:
        print("❌ No audio chunks found in session")
    else:
        # Run VAD analysis
        vad_results, vad_probabilities = run_vad_analysis(audio_chunks)
        
        # Detect speech segments
        speech_segments = detect_speech_segments(vad_results, timestamps)

def display_audio_with_info(audio_data, title, sample_rate=SAMPLE_RATE):
    """Display audio with information panel."""
    if len(audio_data) == 0:
        print(f"❌ No audio data for {title}")
        return
    
    duration = len(audio_data) / sample_rate
    
    # Create styled info panel
    info_html = f"""
    <div style="
        margin: 15px 0; 
        padding: 15px; 
        border: 2px solid #4CAF50; 
        border-radius: 10px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    ">
        <h3 style="margin: 0 0 10px 0; color: #2E7D32;">🎵 {title}</h3>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">
            <div><strong>⏱️ Duration:</strong> {duration:.2f} seconds</div>
            <div><strong>🎚️ Sample Rate:</strong> {sample_rate:,} Hz</div>
            <div><strong>📊 Samples:</strong> {len(audio_data):,}</div>
            <div><strong>💾 Size:</strong> {len(audio_data) * 4 / 1024:.1f} KB</div>
        </div>
    </div>
    """
    
    # Display info panel
    display(HTML(info_html))
    
    # Display audio player
    audio_player = Audio(audio_data, rate=sample_rate, autoplay=False)
    display(audio_player)
    
    return audio_player

def reconstruct_full_audio(audio_chunks):
    """Reconstruct the complete session audio."""
    if len(audio_chunks) == 0:
        return np.array([])
    
    print(f"🔧 Reconstructing audio from {len(audio_chunks)} chunks...")
    
    # Concatenate all audio chunks
    full_audio = np.concatenate(audio_chunks)
    
    # Convert to float32 for audio processing
    audio_float = full_audio.astype(np.float32) / 32768.0
    
    print(f"✅ Audio reconstructed: {len(audio_float):,} samples, {len(audio_float)/SAMPLE_RATE:.2f} seconds")
    
    return audio_float

def extract_speech_segment_audio(audio_chunks, segment):
    """Extract audio for a specific speech segment."""
    if 'chunks' not in segment or len(segment['chunks']) == 0:
        return np.array([])
    
    # Get audio chunks for this segment
    segment_chunks = [audio_chunks[i] for i in segment['chunks'] if i < len(audio_chunks)]
    
    if len(segment_chunks) == 0:
        return np.array([])
    
    # Concatenate segment chunks
    segment_audio = np.concatenate(segment_chunks)
    
    # Convert to float32
    audio_float = segment_audio.astype(np.float32) / 32768.0
    
    return audio_float

# Main audio playback section
if 'audio_chunks' in locals() and len(audio_chunks) > 0:
    print("🎵 === AUDIO PLAYBACK SECTION ===")
    print()
    
    # 1. Full Session Audio
    print("🎧 FULL SESSION AUDIO")
    print("=" * 50)
    full_audio = reconstruct_full_audio(audio_chunks)
    
    if len(full_audio) > 0:
        total_duration = len(full_audio) / SAMPLE_RATE
        display_audio_with_info(
            full_audio, 
            f"Complete Session Audio ({total_duration:.1f}s)"
        )
    else:
        print("❌ Failed to reconstruct full audio")
    
    print("\n" + "="*70 + "\n")
    
    # 2. Individual Speech Segments
    if 'speech_segments' in locals() and len(speech_segments) > 0:
        print(f"🎤 DETECTED SPEECH SEGMENTS ({len(speech_segments)} segments)")
        print("=" * 50)
        
        for i, segment in enumerate(speech_segments):
            print(f"\n🗣️ Speech Segment {i+1}")
            print("-" * 30)
            
            # Extract segment audio
            segment_audio = extract_speech_segment_audio(audio_chunks, segment)
            
            if len(segment_audio) > 0:
                # Calculate timing information
                start_time_sec = (segment['start_time'] - timestamps[0]) / 1000
                duration_sec = segment['duration_ms'] / 1000
                end_time_sec = start_time_sec + duration_sec
                
                # Create detailed title
                title = f"Speech Segment {i+1} | {start_time_sec:.1f}s - {end_time_sec:.1f}s | Duration: {duration_sec:.1f}s"
                
                # Display segment info and audio
                display_audio_with_info(segment_audio, title)
                
                print(f"📍 Timing: {start_time_sec:.1f}s to {end_time_sec:.1f}s")
                print(f"⏱️ Duration: {duration_sec:.1f} seconds")
                print(f"📦 Chunks: {len(segment['chunks'])} audio chunks")
                
            else:
                print(f"❌ Failed to extract audio for segment {i+1}")
    
    elif 'speech_segments' in locals():
        print("🔇 NO SPEECH SEGMENTS DETECTED")
        print("=" * 50)
        print("No speech was detected in this session.")
        print("This could mean:")
        print("  • The audio contains only background noise")
        print("  • The VAD threshold is too high")
        print("  • The audio quality is too low for detection")
        print("  • The session contains no speech")
    
    else:
        print("❌ Speech analysis not available")
        print("Please run the VAD analysis cells first.")

else:
    print("❌ No audio chunks available")
    print("Please load a session file first.")

def plot_vad_analysis(vad_results, vad_probabilities, timestamps, speech_segments):
    """Plot VAD analysis results and detected speech segments."""
    if len(vad_results) == 0:
        print("No VAD results to plot")
        return
    
    # Convert timestamps to relative seconds
    start_time = timestamps[0]
    time_seconds = [(t - start_time) / 1000 for t in timestamps]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 8), sharex=True)
    
    # Plot VAD probabilities
    ax1.plot(time_seconds, vad_probabilities, 'b-', alpha=0.7, linewidth=1)
    ax1.axhline(y=VAD_THRESHOLD, color='r', linestyle='--', alpha=0.8, label=f'Threshold ({VAD_THRESHOLD})')
    ax1.fill_between(time_seconds, 0, vad_probabilities, alpha=0.3)
    ax1.set_ylabel('VAD Probability')
    ax1.set_title('Voice Activity Detection Analysis')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, 1)
    
    # Plot speech detection (binary)
    speech_binary = [1 if x else 0 for x in vad_results]
    ax2.plot(time_seconds, speech_binary, 'g-', linewidth=2, alpha=0.8)
    ax2.fill_between(time_seconds, 0, speech_binary, alpha=0.4, color='green')
    
    # Highlight detected speech segments
    for i, segment in enumerate(speech_segments):
        start_sec = (segment['start_time'] - start_time) / 1000
        end_sec = (segment['end_time'] - start_time) / 1000
        ax2.axvspan(start_sec, end_sec, alpha=0.3, color='red', 
                   label=f'Speech Segment {i+1}' if i == 0 else '')
    
    ax2.set_ylabel('Speech Detected')
    ax2.set_xlabel('Time (seconds)')
    ax2.set_title('Detected Speech Segments')
    ax2.set_ylim(-0.1, 1.1)
    ax2.grid(True, alpha=0.3)
    if speech_segments:
        ax2.legend()
    
    plt.tight_layout()
    plt.show()
    
    # Print segment details
    if speech_segments:
        print("\n📊 Speech Segment Details:")
        for i, segment in enumerate(speech_segments):
            start_sec = (segment['start_time'] - start_time) / 1000
            end_sec = (segment['end_time'] - start_time) / 1000
            duration_sec = segment['duration_ms'] / 1000
            print(f"  Segment {i+1}: {start_sec:.1f}s - {end_sec:.1f}s (duration: {duration_sec:.1f}s)")
    else:
        print("\n🔇 No speech segments detected")

# Plot the analysis if we have results
if 'vad_results' in locals() and len(vad_results) > 0:
    plot_vad_analysis(vad_results, vad_probabilities, timestamps, speech_segments)