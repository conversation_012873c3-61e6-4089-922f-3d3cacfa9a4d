#!/usr/bin/env python3
"""
Main entry point for the Ambient AI Server.

This module starts the FastAPI server with WebSocket support for receiving
real-time multimodal sensor data from iOS devices.
"""

import uvicorn
from src.ambient_ai_server.services.server.server import create_app


def main():
    """Start the Ambient AI Server."""
    app = create_app()
    
    print("🚀 Starting Ambient AI Server...")
    print("📡 WebSocket endpoint: ws://localhost:8000/ws")
    print("📊 Health check: http://localhost:8000/health")
    print("📈 Analytics: http://localhost:8000/analytics")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        access_log=True
    )


if __name__ == "__main__":
    main()
